# setup_environment.yml
# Should take code from Publish Python Package and reuse also in Run Python Package

name: 'Setup Environment Variables'
description: 'Setup Environment Variables'

inputs:
  brand-name:
    required: false
    type: string
    default: Circlez
  # TODO We didn't manage to have multiple environments using
  #   target-environments: [ play1, dvlp1, prod1 ]
  environment-name:
    required: false
    type: string
    default: play1
  repo-name:
    required: true
    type: string
  branch-name:
    required: false
    type: string
    default: ${{ github.ref }}
    description: "Mostly we use the default branch name"
  repo-directory:
    required: true
    type: string
    # TODO Didn't manage to do it
    #default: ${{ github.event.inputs.repo-name }}
    #default: ${{ inputs.repo-name.default }}
  package-directory:
    required: false
    type: string
    default: .
  # inputs.environment_name we don't need to add it to the testing env:
  # inputs.environment-name is the standard, we need to add it to the testing env:
  is-rds-security-group:
    required: false
    type: string
    default: false
  component-name:
    required: false
    type: string
  #is_run_local:
    #required: false
    #type: string    
  #is_run_remote:
    #required: false
    #type: string
  sql2code-command-line:
    required: false
    type: string        
  is-publish-to-test-pypi:
    required: false
    type: string
    # TODO How can the default value be dependent on the environment
    default: false
  is-publish-to-non-test-pypi:
    required: false
    type: string
    # TODO How can the default value be dependent on the environment
    default: true
  #co: # Better we leave it 'false'
    #required: false
    #type: string    
  #SQL_USERNAME:
    #required: false # required in local, not required in remote
    #type: string
  google-user:
    required: false
    type: string
    default: <EMAIL>
  logger-minimum-severity:
    required: false
    type: string
    default: Warning
  logger-is-write-to-sql:
    required: false
    type: string
    default: false
  is-run-local:
    required: false
    type: boolean
    default: true
  
#secrets:
  #RDS_PASSWORD:
    #required: true
# Map the workflow outputs to job outputs
#outputs:
  #firstword:
    #description: "The first output string"
    #value: ${{ jobs.example_job.outputs.output1 }}
  #secondword:
    #description: "The second output string"
    #value: ${{ jobs.example_job.outputs.output2 }}

#permissions:
  #contents: read

runs:
  using: "composite"

  steps:
  - name: Checkout the code of this repo
    uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
    with:
        # Repository name with owner. For example, actions/checkout
        # Default: ${{ github.repository }}
        repository: circles-zone/${{ inputs.repo-name }}
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
        ref: ${{ inputs.branch-name }}
        # Needed for:
        #   database-mysql-local-python-package (sql2code)
        submodules: true

  # TODO Copied from Publish Python Package, should make Publish Python Package use this

  # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript? https://docs.github.com/en/actions/creating-actions/creating-a-composite-action
  # TODO How can we avoid maintaining this step in more than one place (i.e. publish_python_package, publish_typescript_package, run_python, run_typescript ...)
  - name: Set environment variables
    shell: bash
    run: |
      #echo "SLACK_USERNAME=Github Actions" >> $GITHUB_ENV
      #echo "SLACK_ICON_EMOJI=:ocotcat:" >> $GITHUB_ENV
      #echo "SLACK_TITLE=This is a GitHub Actions build!" >> $GITHUB_ENV
      #echo "SLACK_WEBHOOK=${{ secrets.SLACK_WEBHOOK }}" >> $GITHUB_ENV
      #echo "SLACK_CHANNEL=#channel" >> $GITHUB_ENV

      #if [[ ${{ needs.build.result }} == success ]]; then
      if [[ ${{ !inputs.is-run-anonymous  }} ]]; then
        echo "PRODUCT_USER_IDENTIFIER=${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        echo "PRODUCT_PASSWORD=${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
      #else
      fi

      if [[ ${{ inputs.is-run-local }} ]]; then
        echo "is-run-local==true so setup RDS environment"
        echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
        echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        # In local and remote which need access to the database to get_test_entity_id()
        # Sanity tests might need other permission from the package runtime in production / serverless deployment
      else # inputs.is-run-remote
        echo "Not RDS repo"
      fi

      # TODO I'm not sure what is REDDIT_USERNAME - I'm guessing
      if ${{ inputs.repo-name == 'profile-reddit-restapi-imp-local-python-package' }}
      then
        echo "Setup REDDIT Environment Variables"
        echo "REDDIT_USERNAME=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
        # TODO Should be included only in circles-zone/profile-reddit-restapi-imp-local-python-package
        echo "REDDIT_CLIENT_ID=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
        echo "REDDIT_CLIENT_SECRET=${{ secrets.REDDIT_CLIENT_SECRET }}" >> $GITHUB_ENV
      else
        echo "Not REDDIT repo"
      fi

      if ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }}
      then
        echo "Setup ZOOMINFO Environment Variables"
        echo "ZOOMINFO_APPLICATION_CLIENT_ID=${{ vars.ZOOMINFO_APPLICATION_CLIENT_ID }}" >> $GITHUB_ENV
        echo "ZOOMINFO_APPLICATION_ACCOUNT_ID=${{ vars.ZOOMINFO_APPLICATION_ACCOUNT_ID }}" >> $GITHUB_ENV
        echo "ZOOMINFO_APPLICATION_CLIENT_SECRET=${{ secrets.ZOOMINFO_APPLICATION_CLIENT_SECRET }}" >> $GITHUB_ENV

        echo "TEST_ZOOMINFO_USER_ID=${{ vars.TEST_ZOOMINFO_USER_ID }}" >> $GITHUB_ENV
        echo "TEST_ZOOMINFO_FIRST_NAME=${{ vars.TEST_ZOOMINFO_FIRST_NAME }}" >> $GITHUB_ENV
        echo "TEST_ZOOMINFO_LAST_NAME=${{ vars.TEST_ZOOMINFO_LAST_NAME }}" >> $GITHUB_ENV
        echo "TEST_ZOOMINFO_EMAIL=${{ vars.TEST_ZOOMINFO_EMAIL }}" >> $GITHUB_ENV
      else
        echo "Not ZOOMINFO repo"
      fi

      if ${{ inputs.repo-name == 'location-main-local-python-package' }} || ${{ inputs.repo-name == 'real-estate-realtor-com-selenium-imp-local-python-package' }} || ${{ inputs.repo-name == 'profile-main-local-python-package' }} || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package' }} || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} || ${{ inputs.repo-name=='contact-location-local-python-package' }} || ${{inputs.repo-name=='google-contact-local-python-package'}} ; then
        echo "Setup Opencage Environment Variables"
        echo "OPENCAGE_KEY=${{ secrets.OPENCAGE_KEY }}" >> $GITHUB_ENV
      else
        echo "Not Opencage repo"
      fi

      if ${{ inputs.repo-name == 'message-main-local-python-package' }} || ${{ inputs.repo-name == 'message-main-local-python-package' }} ; then
        echo "Setup Messages Environment Variables"
        echo "MESSAGE_TEST_SENDER_PROFILE_ID=${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
      else
        echo "Not a Messages repo"
      fi

      if ${{ inputs.repo-name == 'business-profile-yelp-graphql-imp-local-python-package' }} ; then
        echo "Setup Yelp Environment Variables"
        echo "YELP_API_KEY=${{ vars.YELP_API_KEY }}" >> $GITHUB_ENV
      else
        echo "Not a Yelp repo"
      fi

      if ${{ inputs.repo-name == 'google-contact-local-python-package' }} ; then
        echo "Setup Google Authentication Environment Variables"
        echo "GOOGLE_CLIENT_ID=${{ vars.GOOGLE_CLIENT_ID }}" >> $GITHUB_ENV
        echo "GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}" >> $GITHUB_ENV
        echo "GOOGLE_PROJECT_ID=${{ vars[format('GOOGLE_PROJECT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        echo "GOOGLE_PORT_FOR_AUTHENTICATION=${{ vars.GOOGLE_PORT_FOR_AUTHENTICATION }}" >> $GITHUB_ENV
        echo "GOOGLE_REDIRECT_URIS=http://localhost:54219/" >> $GITHUB_ENV
        echo "GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth" >> $GITHUB_ENV
        # TODO Currently we have both contact-google... repo variable and hardcoded here, which one shall we use?
        echo "GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token" >> $GITHUB_ENV
        echo "GOOGLE_CONTACT_SEQ_START=0" >> $GITHUB_ENV
        echo "GOOGLE_CONTACT_SEQ_END=5" >> $GITHUB_ENV
      else
        echo "Not a Google Authentication repo"
      fi

      IS_NEED_AWS_ACCESS=false

      # Must be before AWS Access
      if ${{ inputs.repo-name == 'storage-main-local-python-package' }} || ${{ inputs.repo-name == 'profile-main-local-python-package' }} || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} || ${{ inputs.repo-name == 'internet-domain-local-python-package' }} || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} || ${{ inputs.repo-name=='contact-person-profile-csv-imp-local-python-package'}} || ${{inputs.repo-name=='google-contact-local-python-package'}}; then
        echo "Setup AWS Storage Environment Variables"
        echo "AWS_DEFAULT_STORAGE_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        IS_NEED_AWS_ACCESS=true
      else
        echo "Not AWS Storage repo"
      fi

      if [[ -z "${IS_NEED_AWS_ACCESS}" ]]; then
        echo "IS_NEED_AWS_ACCESS is undefined"
      #else
        #echo IS_NEED_AWS_ACCESS="${IS_NEED_AWS_ACCESS}"
      fi

      # Must be after AWS Storage
      echo is_need_aws_access=$IS_NEED_AWS_ACCESS
      if [[ "$IS_NEED_AWS_ACCESS"=='true' ]]; then
        echo "Setup AWS Access Environment Variables"
        #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
        echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
        #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        echo "AWS_ACCESS_KEY_ID=${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        echo "AWS_SECRET_ACCESS_KEY=${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
      else
        echo "Not AWS Access repo"
      fi
