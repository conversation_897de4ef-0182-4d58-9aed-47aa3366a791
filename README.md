# Google Account Local Python Package

This package provides OAuth 2.0 authentication with Google services. It handles the full authentication flow, token management, and user verification for Google-based integrations.

## Overview

The `google-account-local-python-package` implements a robust authentication process with Google services using OAuth 2.0. It's designed to:

- Handle the OAuth 2.0 authorization flow with Google
- Manage access and refresh tokens in a database
- Provide token refresh capabilities when tokens expire
- Verify that the authenticated Google account matches the expected user
- Support various Google API scopes (email, contacts, etc.)

## Authentication Process

The authentication process works as follows:

One-time authentication by running onetime_auth.py

1. **Credential Verification**:
   - The system first checks for existing valid credentials
   - If credentials exist but are expired, it automatically refreshes them
   - If no credentials exist, it searches for stored refresh tokens in the database

2. **Token Management**:
   - If a valid refresh token is found, it's used to generate new credentials
   - If refresh fails or no token exists, a new authorization flow is initiated
   - Both access and refresh tokens are stored in the database for future use

3. **Authorization Flow**:
   - An authorization URL is generated and presented to the user
   - The user must visit this URL and grant the requested permissions
   - The system polls for the authorization code in the database
   - Once found, the code is exchanged for OAuth tokens

4. **User Verification**:
   - The system verifies that the authenticated Google account matches the expected email
   - User information is retrieved through Google's API

## Required Environment Variables

```
GOOGLE_CLIENT_ID - OAuth 2.0 client ID from Google Cloud Console
GOOGLE_CLIENT_SECRET - OAuth 2.0 client secret
GOOGLE_REDIRECT_URIS - Authorized redirect URIs (e.g., http://localhost:54415/)
GOOGLE_AUTH_URI - Google's authorization endpoint
GOOGLE_TOKEN_URI - Google's token endpoint
GOOGLE_USER_EXTERNAL_USERNAME - Test username for external system
```

## Usage Example

```python
from google_account_local.src.google_account_local import GoogleAccountLocal

# Initialize the Google Account client
google_account = GoogleAccountLocal()

# Authenticate with Google (may require user interaction in browser)
google_account.authenticate(email="<EMAIL>")

# Get authenticated email
email = google_account.get_email_address()
```

## Development Setup

To set up the environment, follow these steps:

```shell
git clone https://github.com/circles-zone/google-account-local-python-package.git
cd google-account-local-python-package
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

## Testing

We use pytest for testing:

```shell
pytest google_account_local/tests/
```

Note: Some tests require user interaction as they trigger the actual Google OAuth flow.

## Database Integration

The package integrates with a MySQL database to store:
- User external information
- OAuth tokens (access and refresh)
- OAuth state parameters

Tables used:
- `user_external_table` - Stores user external system information
- `token__user_external_table` - Stores OAuth tokens



https://console.cloud.google.com/welcome
