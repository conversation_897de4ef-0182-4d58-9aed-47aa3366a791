variable "github_token" {
  description = "GitHub personal access token"
  type        = string
  sensitive   = true
}

variable "google_client_id" {
  description = "Google OAuth client ID"
  type        = string
}

variable "google_client_secret" {
  description = "Google OAuth client secret"
  type        = string
  sensitive   = true
}

terraform {
  required_providers {
    github = {
      source  = "integrations/github"
      version = "~> 5.0"
    }
  }
}

provider "github" {
  token = var.github_token
  owner = "circles-zone"
}

resource "github_actions_organization_variable" "google_client_id" {
  variable_name = "GOOGLE_CLIENT_ID_DVLP1"
  value         = var.google_client_id
  visibility    = "all"
}

resource "github_actions_organization_secret" "google_client_secret" {
  secret_name   = "GOOGLE_CLIENT_SECRET_DVLP1"
  plaintext_value = var.google_client_secret
  visibility    = "all"
}