Create Terrform called create_google_account_gcp_project.tf which creats project called "Google Accout+$environment_name (i.e. dvlp1). Enable the following APIs People API, Google Sheets API. Create Credentials OAuth 2.0 Clients ID called "Google Account"+$environment_name+" Web OAuth Client" with Auttorised redirect URIs based on $authorised_redirect_uris_array i.e. "http://circlez.ai"


# TODO GCP Project Dashboard, Project Info, Add people to "Google Account Dvlp1" gcp project; <NAME_EMAIL> with role Basic

# TODO # APIs & Services, Credentials, Configure consent screen, App name "Google Account Dvlp1 Web OAuth Client", support email "<EMAIL>",  audience: external, contact information email: dvlp1@circlez.<NAME_EMAIL>, Create OAuth 2.0 Client IDs

# TODO In prod1, Publish App to production

# TODO Since it is not prod1 and external, Add test users which are Google Account/Google Workspace users, 'tal.rosenberg<PERSON>@gmail.com', '<EMAIL>' (Google Group) and '<EMAIL>' (I didn't manage <NAME_EMAIL>)

# TODO Select Google Project, Clients, OAuth 2.0 Client IDs, Application Type: Web Application, name: "Google Account Dvlp1 Web OAuth Client", Add Authorized redirect URIs "https://circlez.ai", http://localhost:54415/? and get client_id and client_secret

sudo snap install google-cloud-cli --classic

# Create /home/<USER>/.config/gcloud/application_default_credentials.json
gcloud auth application-default login
# cd ~/.config/gcloud/
# You can browser https://cloud.google.com/sdk/auth_success

export GOOGLE_APPLICATION_CREDENTIALS=~/.config/gcloud/application_default_credentials_tal_circlez_ai.json
echo 'export GOOGLE_APPLICATION_CREDENTIALS=~/.config/gcloud/application_default_credentials_tal_circlez_ai.json' >> ~/.bashrc

sudo snap install terraform --classic

terraform init
source /repos/circlez/tal-circlez.ai/ubuntu/bin/set_gcp
# export TF_VAR_billing_account="YOUR_BILLING_ACCOUNT_ID"
terraform plan -var="environment_name=dvlp1"
terraform apply -var="environment_name=dvlp1"

# If terraform plan/apply do not work
terraform init -upgrade
