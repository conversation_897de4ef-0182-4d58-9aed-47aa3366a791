variable "environment_name" {
  description = "Environment name (e.g., dvlp1)"
  type        = string
}

variable "authorised_redirect_uris_array" {
  description = "Array of authorized redirect URIs"
  type        = list(string)
  default     = ["http://circlez.ai", "https://circlez.ai/play1"]
}

variable "billing_account" {
  description = "Billing account ID"
  type        = string
}

resource "google_project" "google_account_project" {
  name            = "Google Account ${var.environment_name}"
  project_id      = "gogle-account-${var.environment_name}-${random_id.project_suffix.hex}"
  billing_account = var.billing_account
}

resource "random_id" "project_suffix" {
  byte_length = 4
}

resource "google_project_service" "people_api" {
  project = google_project.google_account_project.project_id
  service = "people.googleapis.com"
}

resource "google_project_service" "sheets_api" {
  project = google_project.google_account_project.project_id
  service = "sheets.googleapis.com"
}

output "project_id" {
  value = google_project.google_account_project.project_id
}

resource "null_resource" "create_oauth_client" {
  provisioner "local-exec" {
    command = <<-EOT
      gcloud auth application-default set-quota-project ${google_project.google_account_project.project_id}
      
      # Create OAuth consent screen
      gcloud alpha iap oauth-brands create \
        --application_title="Google Account ${var.environment_name}" \
        --support_email=$(gcloud config get-value account) \
        --project=${google_project.google_account_project.project_id} || true
      
      # Create OAuth client
      gcloud alpha iap oauth-clients create \
        --brand=$(gcloud alpha iap oauth-brands list --project=${google_project.google_account_project.project_id} --format="value(name)") \
        --display_name="Google Account ${var.environment_name} Web OAuth Client" \
        --project=${google_project.google_account_project.project_id}
    EOT
  }
  
  depends_on = [
    google_project_service.people_api,
    google_project_service.sheets_api
  ]
}

output "oauth_setup_instructions" {
  value = <<-EOT
    OAuth 2.0 Client created automatically via gcloud CLI.
    Authorized redirect URIs must be added manually:
    1. Go to: https://console.cloud.google.com/apis/credentials?project=${google_project.google_account_project.project_id}
    2. Edit the OAuth client
    3. Add redirect URIs: ${join(", ", var.authorised_redirect_uris_array)}
  EOT
}