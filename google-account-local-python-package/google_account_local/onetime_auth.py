# from src.google_account_local_copy import GoogleAccountLocal
from src.google_account_local import GoogleAccountLocal
from python_sdk_remote.utilities import our_get_env


def one_time_auth(email: str):
    """
    Perform one-time authentication for Google Account Local.
    This function will create a new instance of GoogleAccountLocal and
    call the authenticate method to perform the authentication process.
    """
    # Create an instance of GoogleAccountLocal
    google_account_local = GoogleAccountLocal()

    # Perform the authentication process
    google_account_local.authenticate(email=email)
    # Return the authenticated instance
    return google_account_local


if __name__ == "__main__":
    # Example usage
    email = our_get_env("GOOGLE_USER_EXTERNAL_USERNAME")
    google_account_local = one_time_auth(email)
