# Needed in the begining of the test*.py in VSCode (probbably not needed in PyCharm)
from dotenv import load_dotenv

import pytest  # noqa
from logger_local.LoggerLocal import Logger  # noqa
import os  # noqa
import sys  # noqa
from datetime import datetime, timezone, timedelta
from user_external_local.token__user_external import TokenUserExternals

script_directory = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(script_directory, ".."))

from src.google_account_local import GoogleAccountLocal  # noqa
from src.google_account_local_constants import GoogleAccountLocalConstants  # noqa
from python_sdk_remote.utilities import our_get_env  # noqa: E402
load_dotenv()


# Logger setup
logger = Logger.create_logger(
    object=GoogleAccountLocalConstants.LoggerSetupConstants.GOOGLE_ACCOUNT_LOCAL_PYTHON_PACKAGE_TEST_LOGGER_OBJECT
)


# TODO: The token lasts for about an hour, so we must run google_contacts.authenticate() manually. We should try to find
# a solution based on <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Cypress ...
# before GHA runs test_pull_contacts_with_stored_token(). we want to try and make google_contacts.authenticate()

GOOGLE_USER_EXTERNAL_USERNAME = our_get_env("GOOGLE_USER_EXTERNAL_USERNAME", raise_if_empty=True)

# TODO If there is no valid refresh token or re-connect failed, call the bellow google_contacts.authenticate()
# TODO Since both the google_account_reconnect() and authenticate() are needed for all Google services/APIs shall we take
# them out of GoogleContacts Class?
# TODO Please call empty GoogleCalendar methods to demonstrate we log in once to the Google Account and Sync multiple services


@pytest.fixture
def google_account_local():
    # TODO Take all literals such as "GOOGLE_ACCOUNT" to constants per the standard in python-package-template
    google_account_local = GoogleAccountLocal(google_environment_variables_prefix="GOOGLE_ACCOUNT")
    return google_account_local


def test_authenticate(google_account_local: GoogleAccountLocal):
    # Sometimes this test requires user interaction on the browser
    try:
        # TODO We should allow for a single Circlez.ai User (PRODUCT_USER_IDENTIFIER) to use multiple Google
        # Accounts - I assume it works, let's check it and call in the tests to multiple user_external
        google_account_local.authenticate(email=GOOGLE_USER_EXTERNAL_USERNAME)
    except Exception as exception:
        logger.exception(object={"exception": exception})
        raise exception

    # Assert
    assert True


def test_auto_refresh_token(google_account_local: GoogleAccountLocal):

    profile_id = google_account_local.profile_local.get_profile_id_by_email_address(
        email_address=GOOGLE_USER_EXTERNAL_USERNAME,
    )

    auth_details = google_account_local.user_externals_local.get_auth_details_by_profile_id_system_id_username(
        system_id=GoogleAccountLocalConstants.GOOGLE_SYSTEM_ID,
        username=GOOGLE_USER_EXTERNAL_USERNAME,
        profile_id=profile_id
    )

    expiry = auth_details.get("expiry")
    is_refresh_token_valid = auth_details.get("is_refresh_token_valid")
    refresh_token = auth_details.get("refresh_token")
    user_external_id = auth_details.get("user_external_id")

    is_expired = google_account_local.check_if_expired(
        expiry=expiry
    )
    if not is_expired:
        mocked_expiry = (datetime.now(timezone.utc) - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

        data_dict = {
            "expiry": mocked_expiry,
        }

        # TODO token_user_externals = 
        token_user_external = TokenUserExternals()
        updated_rows = token_user_external.update_by_column_and_value(
            column_name="user_external_id",
            column_value=user_external_id,
            data_dict=data_dict,
            order_by="updated_timestamp",
            limit=1,
        )
        
        assert updated_rows == 1, "Token should be updated in the database"
        auth_details_after = google_account_local.user_externals_local.get_auth_details_by_profile_id_system_id_username(
            system_id=GoogleAccountLocalConstants.GOOGLE_SYSTEM_ID,
            username=GOOGLE_USER_EXTERNAL_USERNAME,
            profile_id=profile_id
        )
        expiry_after = auth_details_after.get("expiry")

        assert expiry_after == mocked_expiry, "Token should be updated in the database"
        expiry = expiry_after

    is_expired = google_account_local.check_if_expired(
        expiry=expiry
    )

    assert is_expired is True, "Token should be expired"

    if is_expired and is_refresh_token_valid and refresh_token and user_external_id:
        google_account_local.authenticate(
            email=GOOGLE_USER_EXTERNAL_USERNAME
        )

        auth_details_refreshed = google_account_local.user_externals_local.get_auth_details_by_profile_id_system_id_username(
            system_id=GoogleAccountLocalConstants.GOOGLE_SYSTEM_ID,
            username=GOOGLE_USER_EXTERNAL_USERNAME,
            profile_id=profile_id
        )

        expiry_after = auth_details_refreshed.get("expiry")

        is_expired_refreshed = google_account_local.check_if_expired(
            expiry=expiry_after
        )

        assert is_expired_refreshed is False, "Token should not be expired after refresh"


if __name__ == "__main__":
    # pytest.main(['-s', 'google-account-local-python-package/google_account_local/tests/' +
    #              'test_google_account_local.py::test_authenticate'])
    google_account_local_obj = GoogleAccountLocal()
    # test_authenticate(google_account_local_obj)
    test_auto_refresh_token(google_account_local_obj)
