import mysql.connector

# TODO: We should use the our Database package instead of connecting directly to the database

# We should have in /db folder Python file per each table (with the exception of XXX_ml_table that will be in the same Python file as the XXX_table), the filename will be the table name  # noqa E501

# We should create a generic class to be used for create, upgrade or downgrade each table
# The new class should update the database schema (i.e. dependencies) in the master database
# The package should use "Environment" Environment variable and the URL package determine the hostname
# The package should use "MasterDB_*" to get the credentials for the master database

# Set up database connection
mydb = mysql.connector.connect(
    host="your_host",
    user="your_username",
    password="your_password",
    database="your_database",
)


def create_message_channel_table():
    cursor = mydb.cursor()

    # Create schema if not exists
    cursor.execute("CREATE SCHEMA IF NOT EXISTS person")
    cursor.execute("USE person")

    # Create person table
    message_channel_create_table_ddl = """
    CREATE TABLE IF NOT EXISTS message_channel (
    id BIGINT UNSIGNED AUTO_INCREMENT NOT NULL,
    provider_id BIGINT UNSIGNED,
    <PERSON><PERSON><PERSON><PERSON>EY(id),
    <PERSON>OREI<PERSON><PERSON> KEY(provider_id) REFERENCES provider(id)
    )
    """
    cursor.execute(message_channel_create_table_ddl)

    message_channel_ml_create_table_ddl = """
    CREATE TABLE IF NOT EXISTS person_ml (
    id BIGINT UNSIGNED AUTO_INCREMENT,
    message_channel_id BIGINT UNSIGNED NOT NULL,
    provider_id BIGINT UNSIGNED,
    lang_code CHAR(5),
    PRIMARY KEY(id),
    FOREIGN KEY(message_channel_id) REFERENCES message_channel(id),
    FOREIGN KEY(provider_id) REFERENCES provider(id)
    );
    """
    cursor.execute(message_channel_ml_create_table_ddl)

    mydb.commit()
    cursor.close()
    mydb.close()
