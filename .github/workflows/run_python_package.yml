# Local Package used by  
#   https://github.com/circles-zone/database-mysql-local-python-package/blob/dev/.github/workflows/build-publish-database-mysql-local-python-package-play1.yml
# Remote Package dev+Feature branch
#   https://github.com/circles-zone/group-local-restapi-typescript-serverless-com/tree/BU-1234-Add-GROUP_ENTITY_TYPE_ID/.github/workflows
#   https://github.com/circles-zone/event-local-restapi-python-serverless-com/blob/dev/.github/workflows/build_deploy_event_local_restapi_python_serverless_com_play1.yml
# Template- used by
#   https://github.com/circles-zone/python-package-template/blob/dev/.github/workflows/build_publish_project_name_python_package_play1.yml

name: Run Python Package  

on: 
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      component-name:
        required: false
        type: string
        default: ''
      repo-name:
        required: true
        type: string
      branch-name:
        required: false
        type: string
        default: dev
      repo-directory:
        required: true
        type: string
      package-directory:
        required: false
        type: string
        default: .
      #is-rds-security-group:
        #required: false
        # TODO Can we make it boolean?
        #type: string
        #default: false
      google-user:
        required: false
        type: string
        # We don't want always to login to Google, passed to run_python_tests reusable workflow
        #default: '<EMAIL>'
        default: ''
      # Run google-contact-local-python-packge only if the user authenticated interactivly manually before running the job
      is-google-user-authenticated-manually:
        required: false
        type: boolean
        default: false
      is-run-local:
        required: false
        type: boolean
        # We should have true as default otherwise there are cases which using the default and not test are running
        default: true
      is-run-remote:
        required: false
        type: boolean
        default: false
      # We should try to run create-user, smartlink with is-run-anonymous true
      is-run-anonymous:
        required: false
        type: boolean
        default: false
      #is-debug-mode:
      logger-minimum-severity:
        required: false
        type: string
        default: Warning
      # TODO Shall we call it logger-is-write-to-sql or is-logger-write-to-sql?
      logger-is-write-to-sql:
        required: false
        type: boolean
        default: false
jobs:        
  # Run remote-Python-package to test the deployment
  # TODO can we add ${{ inputs.package-directory }} in the job name?
  run_remote_python:
    # TODO We also have run_python_tests.yml without Build
    name: Run Python Package- Build and Run Tests

    # As google-contact-local-python-package is not working currently there is no point in trying to run it
    #if: ${{contains(github.event.head_commit.message, '[pub]') && !(inputs.repo-name=='google-contact-local-python-package') }}
    # As google-contact-local-python-package is working currently
    #if: ${{contains(github.event.head_commit.message, '[pub]') }}
    if: |
      contains(github.event.head_commit.message, '[pub]')
      && ( (inputs.repo-name=='google-contact-local-python-package' && inputs.is-google-user-authenticated-manually)
      || !(inputs.repo-name=='google-contact-local-python-package') )


    # TODO Can we have if: in the job level?
    #if: inputs.is_run_user_context_remote == true
    # Trying to make the python_run work if at least one of the previous jobs succeeded (i.e. variable_local_python_package)
    # This probably means that the whole workflow will be Green even if this job fails

    #continue-on-error: true
    runs-on: ubuntu-latest
    # ContPerProCsvImpLocalP(dev)Play1 needs more than 5 minutes
    # GooConntLocP meeds more then 10 minutes with Logger level of Info
    timeout-minutes: 15
    #env:
      # TODO The directory in the top level (should be exactly as the name of the repo)
      #repo_name: user_context_remote_python_package
    environment:
      name: ${{ inputs.environment-name }} # play1 # If using $environment-name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ inputs.environment-name }}.circ.zone
    strategy:
      # Allow the play1 job to work even if the dvlp1 job failed due to environment protection rules.
      #fail-fast: false
      matrix:
        #python-version: ["3.11"]
        python-version: ["3.x"]
    steps:
    - name: Checkout code
      uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
        # Repository name with owner. For example, actions/checkout
        # Default: ${{ github.repository }}
        repository: circles-zone/${{ inputs.repo-name }}
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
        ref: ${{ inputs.branch-name }}

    - name: Install dependencies
      run: |
        echo "pwd"
        pwd
        echo "ls -lag"
        ls -lag
        #TODO We have issue when runing it from one repo on another repo. i.e. group-main-local-restapi-typescript-serverless-com Sanity Tests
        cd ./${{ inputs.repo-directory }}
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Build package
      run: |
        cd ./${{ inputs.repo-directory }} #/${{ inputs.package-directory }}
        pip install build
        python -m build

    # TODO Old - Trying to replace it

    # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript?
    # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript? https://docs.github.com/en/actions/creating-actions/creating-a-composite-action
    # TODO How can we avoid maintaining this step in more than one place (i.e. publish_python_package, publish_typescript_package, run_python, run_typescript ...)
    - name: Set environment variables
      run: |
        #echo "SLACK_USERNAME=Github Actions" >> $GITHUB_ENV
        #echo "SLACK_ICON_EMOJI=:ocotcat:" >> $GITHUB_ENV
        #echo "SLACK_TITLE=This is a GitHub Actions build!" >> $GITHUB_ENV
        #echo "SLACK_WEBHOOK=${{ secrets.SLACK_WEBHOOK }}" >> $GITHUB_ENV
        #echo "SLACK_CHANNEL=#channel" >> $GITHUB_ENV
  
        #if [[ ${{ needs.build.result }} == success ]]; then
        if [[ ${{ !inputs.is-run-anonymous  }} ]]; then
          echo "PRODUCT_USER_IDENTIFIER=${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "PRODUCT_PASSWORD=${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        #else
        fi

        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "is-run-local==true so setup RDS environment"
          echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
          echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        else # inputs.is-run-remote
          echo "Not RDS repo"
        fi

        # TODO I'm not sure what is REDDIT_USERNAME - I'm guessing
        if ${{ inputs.repo-name == 'profile-reddit-restapi-imp-local-python-package' }}
        then
          echo "Setup REDDIT Environment Variables"
          echo "REDDIT_USERNAME=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
          # TODO Should be included only in circles-zone/profile-reddit-restapi-imp-local-python-package
          echo "REDDIT_CLIENT_ID=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
          echo "REDDIT_CLIENT_SECRET=${{ secrets.REDDIT_CLIENT_SECRET }}" >> $GITHUB_ENV
        else
          echo "Not REDDIT repo"
        fi

        if ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }}
        then
          echo "Setup ZOOMINFO Environment Variables"
          echo "ZOOMINFO_APPLICATION_CLIENT_ID=${{ vars.ZOOMINFO_APPLICATION_CLIENT_ID }}" >> $GITHUB_ENV
          echo "ZOOMINFO_APPLICATION_ACCOUNT_ID=${{ vars.ZOOMINFO_APPLICATION_ACCOUNT_ID }}" >> $GITHUB_ENV
          echo "ZOOMINFO_APPLICATION_CLIENT_SECRET=${{ secrets.ZOOMINFO_APPLICATION_CLIENT_SECRET }}" >> $GITHUB_ENV

          echo "TEST_ZOOMINFO_USER_ID=${{ vars.TEST_ZOOMINFO_USER_ID }}" >> $GITHUB_ENV
          echo "TEST_ZOOMINFO_FIRST_NAME=${{ vars.TEST_ZOOMINFO_FIRST_NAME }}" >> $GITHUB_ENV
          echo "TEST_ZOOMINFO_LAST_NAME=${{ vars.TEST_ZOOMINFO_LAST_NAME }}" >> $GITHUB_ENV
          echo "TEST_ZOOMINFO_EMAIL=${{ vars.TEST_ZOOMINFO_EMAIL }}" >> $GITHUB_ENV
        else
          echo "Not ZOOMINFO repo"
        fi

        # location-local-python-package Temp for backward compatibility in packages running using the old repo name \
        if ${{ inputs.repo-name == 'location-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'location-local-python-package' }} \
          || ${{ inputs.repo-name == 'real-estate-realtor-com-selenium-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-location-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-contact-local-python-package' }} ; then
          echo "Setup Opencage Environment Variables"
          echo "OPENCAGE_KEY=${{ secrets.OPENCAGE_KEY }}" >> $GITHUB_ENV
        else
          echo "Not Opencage repo"
        fi

        if ${{ inputs.repo-name == 'message-main-local-python-package' }} || ${{ inputs.repo-name == 'message-main-local-python-package' }} ; then
          echo "Setup Messages Environment Variables"
          echo "MESSAGE_TEST_SENDER_PROFILE_ID=${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Messages repo"
        fi

        if ${{ inputs.repo-name == 'business-profile-yelp-graphql-imp-local-python-package' }} ; then
          echo "Setup Yelp Environment Variables"
          echo "YELP_API_KEY=${{ vars.YELP_API_KEY }}" >> $GITHUB_ENV
        else
          echo "Not a Yelp repo"
        fi

        # TEMP
        echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV

        if ${{ inputs.repo-name == 'google-contact-local-python-package' }} \
        || ${{ inputs.repo-name == 'google-account-local-python-package' }} \
        || ${{ inputs.repo-name == 'user-external-main-local-python-package' }}; then
          echo "Setup Google Authentication Environment Variables"
          # echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV

          echo "GOOGLE_PROJECT_ID=${{ vars[format('GOOGLE_PROJECT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_PORT_FOR_AUTHENTICATION=${{ vars.GOOGLE_PORT_FOR_AUTHENTICATION }}" >> $GITHUB_ENV
          # TODO Will it work in GHA? No, Interactive Google Authentication do not work in GHA. Do we need it here? If so, can we make it GHA Variable and not hard coded? We have it also in GCP Project Auth 2.0. Why in both?
          #echo "GOOGLE_REDIRECT_URIS=http://localhost:54219/" >> $GITHUB_ENV
          echo "GOOGLE_REDIRECT_URIS=https://gg6svciji8.execute-api.us-east-1.amazonaws.com/play1/api/v1/googleAccount/getCode" >> $GITHUB_ENV
          echo "GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth" >> $GITHUB_ENV
          # TODO Currently we have both contact-google... repo variable and hardcoded here, which one shall we use?
          echo "GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token" >> $GITHUB_ENV
          echo "GOOGLE_CONTACT_SEQ_START=0" >> $GITHUB_ENV
          # With GOOGLE_CONTACT_SEQ_END=5 it GooContLocP Logger Info needs more than 10 minutes
          echo "GOOGLE_CONTACT_SEQ_END=3" >> $GITHUB_ENV
          echo "GOOGLE_USER_EXTERNAL_USERNAME=${{ vars[format('GOOGLE_USER_EXTERNAL_USERNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # TODO I think we not using it
          #echo "GOOGLE_USER_EXTERNAL_PASSWORD=${{ secrets[format('GOOGLE_USER_EXTERNAL_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Google Authentication repo"
        fi 

        IS_NEED_AWS_ACCESS=false

        # Must be before AWS Access
        if ${{ inputs.repo-name == 'storage-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'internet-domain-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package'}} \
          || ${{ inputs.repo-name == 'google-contact-local-python-package'}}; then
          echo "Setup AWS Storage Environment Variables"
          echo "AWS_DEFAULT_STORAGE_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          IS_NEED_AWS_ACCESS=true
        else
          echo "Not AWS Storage repo"
        fi

        if [[ -z "${IS_NEED_AWS_ACCESS}" ]]; then
          echo "IS_NEED_AWS_ACCESS is undefined"
        #else
          #echo IS_NEED_AWS_ACCESS="${IS_NEED_AWS_ACCESS}"
        fi

        # Must be after AWS Storage
        echo is_need_aws_access=$IS_NEED_AWS_ACCESS
        if [[ "$IS_NEED_AWS_ACCESS"=='true' ]]; then
          echo "Setup AWS Access Environment Variables"
          #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
          echo "AWS_ACCESS_KEY_ID=${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not AWS Access repo"
        fi


    # TODO New steps we added instead of the previous one
    #- name: setup_environment_variables.yml
      # No need to run the tests twice
      #if: github.ref=='refs/heads/dev'
      # TODO Disable until we fix it
      #if: false
      # Let's try to run it in parallel to the main job
      #needs: run_remote_python
      #strategy:
        #matrix:
          # TODO We didn't manage to have multiple environments
          #target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
      #uses: circles-zone/github-workflows/setup-environment-variables@main # v1.1
      #uses: ./.github/actions/setup-environment-variables@main # v1.2
      #secrets: inherit
      #uses: circles-zone/github-workflows@main
      #with:
        # TODO We didn't manage to use the env: directly
        #brand-name: Circlez
        #environment-name: ${{ inputs.environment-name }}
        #component-name: 
        #repo-name: ${{ inputs.repo-name }}
        #repo-directory: ${{ inputs.repo-directory }}
        #branch-name: ${{ inputs.branch-name }}
        #package_directory_name: google-contact_local
        #is_rds_security_group: false
        #is_publish_to_non_test_pypi: true
        #google-user: ${{ inputs.google-user }}
        #logger-minimum-severity: ${{ inputs.logger-minimum-severity }}


    # If the "if in the setup environment variables" works we can delete this step
    # TODO I'm not sure it is needed as we need to provide RDS in both, but maybe there are other environment variables
    # In some packages such as smartlink-remote we want to test also without Product Credentials
    - name: This step will be deleted if remote packages works fine without it- Test Remote (with Product Credentials) Package with pytest (Test the published package should be done by the <entity-name]-remote-python-package-graphql/restapi Unit-Test)
    # pytest instead of python -m pytest
      # As we give database access also for remote packages
      if: false
      #if: inputs.is-run-remote==true
      run: |
        cd ./${{ inputs.repo-directory }}
        pip install pytest-cov
        # TODO: Fix this line (i.e. --cov=...) so it will work with your modules
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app tests/ | tee pytest-coverage.txt
        #python -m unittest discover -s tests -p 'test_*.py'
        set -o pipefail
        pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
      env:
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        # TODO remote-anonymous (no product identifier): In packages such as smartlink-remote, create user ... we should test without Product Credentials, how do we do it?
        # TODO remote-product-user: 
        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}

        # In local and remote which needs access to the database to get_test_entity_id()
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}  
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}

        #AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}

        #REGION: ${{ vars.AWS_DEFAULT_REGION }}
        #BUCKET_NAME: ${{ vars.BUCKET_NAME }}
        AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}

        # https://github.com/circles-zone/contact-person-local-python-package (contact-person-local-python-package)
        GOOGLE_PORT_FOR_AUTHENTICATION: ${{ vars.GOOGLE_PORT_FOR_AUTHENTICATION }}

        TEST_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}
        FROM_EMAIL: ${{ vars[format('MESSAGE_FROM_EMAIL_{0}', inputs.environment-name)] }}


    # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript?
    - name: Set environment variables for local (Sanity Tests database access)
      run: |
        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "Setup is-run-local Environment Variables"
          echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
          echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        else
          echo "Not is-run-local"
        fi

    - name: Test with Sanity Test User (Full permission) (i.e. Local Package locally) with pytest (Test the published package should be done by the <entity-name]-remote-python-package-graphql/restapi Unit-Test) with OpenCage
    # pytest instead of python -m pytest
      # TODO Do we need it? Any diff between local and remote? Both need RDS.
      if: inputs.is-run-local == true
      # google-contact-local-python-package needs more than 10 min
      timeout-minutes: 15
      run: |
        cd ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}
        pip install pytest-cov
        # TODO: Fix this line (i.e. --cov=...) so it will work with your modules
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app tests/ | tee pytest-coverage.txt
        #python -m unittest discover -s tests -p 'test_*.py'
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        # pytest Combination
        # pytest-mock needed for variable-local-python-package
        pip install pytest-mock
        set -o pipefail
        # I aded -x
        pytest -x -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
      env:
        # Environment Variables per repo which are not applicative
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        #PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        #PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # TODO If LOGZIO_TOKEN is not working comment the bellow line and make sure logger-local-python works without LOGZIO_TOKEN
        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        # TODO Values from https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        #LOGGER_MINIMUM_SEVERITY: ${{ inputs.logger-minimum-severity }}
        # TODO How do we avoid this variable if it is not mentioned?
        #LOGGER_IS_WRITE_TO_SQL: ${{ inputs.logger-is-write-to-sql }}

        #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}

        # Needed for https://github.com/circles-zone/message-local-python-package
        #AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}
        #BUCKET_NAME: ${{ vars.BUCKET_NAME }}
        #AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}

        # In local and remote which need access to the database to get_test_entity_id()
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
        # Sanity tests might need other permission from the package runtime in production
        #RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}  

        # TODO Should be included only in circles-zone/profile-reddit-restapi-imp-local-python-package
        #REDDIT_CLIENT_ID: ${{ vars.REDDIT_CLIENT_ID }}
        #REDDIT_CLIENT_SECRET: ${{ secrets.REDDIT_CLIENT_SECRET }}

        # location-local-python (database-mysql-python)
        #OPENCAGE_KEY: ${{ secrets.OPENCAGE_KEY }}

        # Needed also in text-block-local-python-package Sanity Tests
        #GOOGLE_CLIENT_ID: ${{ vars.GOOGLE_CLIENT_ID }}
        #GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
        #GOOGLE_REDIRECT_URIS: localhost
        #GOOGLE_AUTH_URI: https://accounts.google.com/o/oauth2/auth
        #GOOGLE_AUTH_URI: http://localhost
        #GOOGLE_TOKEN_URI: http://localhost
        # TODO Add GOOGLE_ prefix
        #GOOGLE_PORT_FOR_AUTHENTICATION: ${{ vars.GOOGLE_PORT_FOR_AUTHENTICATION }}
        #GOOGLE_CONTACT_SEQ_START: 1
        #GOOGLE_CONTACT_SEQ_END: 3

        # Used by: messages-local-python
        # Also used for sanity tests of 
        #   variable-local-python-package
        # Used by: messages-local-python
        #TEST_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}
        # Used by messages-local-python
        # Used by: message-local-python
        #DEFAULT_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_DEFAULT_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}
        # Used by: email-message-aws-ses-local-python-package
        #FROM_EMAIL: ${{ vars[format('MESSAGE_FROM_EMAIL_{0}', inputs.environment-name)] }}
        #AWS_SES_DEFAULT_CONFIGURATION_SET: ${{ vars[format('MESSAGE_AWS_SES_DEFAULT_CONFIGURATION_SET_{0}', inputs.environment-name)] }}
        #TEST_DESTINATION_EMAIL: ${{ vars[format('MESSAGE_TEST_DESTINATION_EMAIL_{0}', inputs.environment-name)] }}
        #REALLY_SEND_EMAIL: ${{ vars[format('MESSAGE_REALLY_SEND_EMAIL_{0}', inputs.environment-name)] }}
        #TEST_DESTINATION_PHONE_NUMBER: ${{ vars[format('MESSAGE_TEST_PHONE_NUMBER_{0}', inputs.environment-name)] }}

        # TODO add AWS_SES_DEFAULT_ prefix
        # Used by: messages-local
        #CONFIGURATION_SET: ${{ vars.AWS_SES_DEFAULT_CONFIGURATION_SET }}


    # TODO Maybe this is causing the error "reusable workflows should be referenced at the top-level `jobs.*.uses' key, not within steps"
    #- name: NEW Test methods/functions on GitHub runner using pytest (Tests of the published package should be done by the <project-name>-graphql/restapi-serverless-com E2E Tests)
      #uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
      #with:
        ##repo-name: message-local-python-package
        #repo-directory: message_local_python_package

  # Not helpful as run_python_tests.yml become exactly like run_python_package.yml
  # Running the run_python_tests.yml didn't work as a step so trying to run it as a job
  #tests_as_part_of_run_python:
    #name: NEW Test methods/functions on GitHub runner using pytest (Tests of the published package should be done by the <project-name>-graphql/restapi-serverless-com E2E Tests)
    #uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
    ## TODO Maybe this is needed so the called Workflow will have the token
    #secrets: inherit
    #with:
      #repo-name: ${{ inputs.repo-name }}
      #branch-name: ${{ inputs.branch-name }}
      #repo-directory: ${{ inputs.repo-directory }}

  # Try to run common run_python_tests.yml
  run-python-tests:
    name: Run Python Tests (w/o build) trying to use google-user
    # No need to run the tests twice
    if: false
    #if: github.ref=='refs/heads/dev'
    # Disable until we fix it
    #if: false
    # Let's try to run it in parallel to the main job
    #needs: run_remote_python
    #strategy:
      #matrix:
        # TODO We didn't manage to have multiple environments
        #target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ inputs.environment-name }}
      #component-name: 
      repo-name: ${{ inputs.repo-name }}
      repo-directory: ${{ inputs.repo-directory }}
      branch-name: ${{ inputs.branch-name }}
      #TODO We should bring it back to dev ASAP
      #branch-name: dev
      #package_directory_name: google-contact_local
      #is_rds_security_group: false
      #is_publish_to_non_test_pypi: true
      google-user: ${{ inputs.google-user }}
      logger-minimum-severity: ${{ inputs.logger-minimum-severity }}
