name: Build Deploy Circlez User React.js Frontend to AWS S3 CloudFront

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: false
        type: string
        default: circles-user-reactjs-frontend
      # Current branch is: ${GITHUB_REF##*/}
      branch-name:
        required: false
        type: string
        default: ${{ github.ref_name }}
      repo-directory:
        required: false
        type: string
        # TODO Not working
        #default: ${{ inputs.repo-name }}
        default: .
      #package_directory_name:
        #required: false
        #type: string
      #is_rds_security_group:
        #required: false
        #type: string
      component-name:
        required: false
        type: string
      #is_run_local:
        #required: false
        #type: string    
      #is_run_remote:
        #required: false
        #type: string
      frontend-s3-bucket-name:
        required: false
        type: string
        default: circlez-user-reactjs-frontend

# env: is incase-sensitive
env:
  brand-name: Circlez
  environment-name: ${{ inputs.environment-name }}
  frontend-s3-bucket-name: circlez-user-reactjs-frontend
  # https://stackoverflow.com/questions/76143517/github-actions-not-passing-environment-variables-to-vite-build
  is_run_playwrite: true
  is_deploy_in_domain_root: true

jobs:
  build_unit_test_and_deploy_in_branch_subdomain:
    name: build_unit_test_and_deploy_in_branch_subdomain
    # TODO Please comment this line
    #if: ${{ false }}
    if: ${{contains(github.event.head_commit.message, '[pub]') }}
    runs-on: ubuntu-latest
    environment:
      name:  ${{ inputs.environment-name }}
      url: http://${{ inputs.environment-name }}.circ.zone
    strategy:
      #fail-fast: false
      matrix:
        node-version: [22.12.0]
    permissions:
      contents: read # read is the default if there are no permissions: . Needed for actions/checkout
      packages: write # TODO Do we need a write permission?
      pull-requests: write # Due to warning
    env:
      # Those variables are available in all steps, AWS_ACCESS_KEY_ID in the steps is hidden
      VITE_BACKEND_URL: ${{ vars.VITE_BACKEND_URL }}
      #AWS_ACCESS_KEY_ID: ${{ vars.AWS_ACCESS_KEY_ID_PLAY1 }}
      AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
      #AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_PLAY1 }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
      # Should the variable be called AWS_DEFAULT_REGION?
      AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}
      #AWS_DEFAULT_REGION: ${{ vars.REGION }}
      #VITE_ENVIRONMENT_NAME: play1
      VITE_ENVIRONMENT_NAME:  ${{ inputs.environment-name }}
      VITE_BRAND_NAME: Circlez

    steps:
    # Need permission: contents: read - which is the default.
    - name: Checkout code
      uses: actions/checkout@main # v4.1.1 https://github.com/actions/checkout
      with:
        submodules: true # To use the email_validator ...
        # TODO Shall we change the secret to start with GH_ like others
        token: ${{ secrets.REACTJS_SUBMODULES_GITHUB_TOKEN }}
        ref: ${{ inputs.branch-name }}

    # Vitest need Node.js 14, Node.js 16 recommended
    - name: Set up Node.js
      uses: actions/setup-node@main # v4.0.0 https://github.com/actions/setup-node
      with:
        #node-version: 20 # 16 -> 18
        node-version: ${{ matrix.node-version }}

    # Probably "run build" needs access to private repo otherwise error "npm ERR! code E401" "npm ERR! 401 Unauthorized - GET" "authentication token not provided", solution is to create .npmrc
    #- name: Authenticate PUT with private GitHub package to avoid 401 (npm ERR! code E401, npm ERR! 401 Unauthorized)
      #run: |
        #echo "@circles-zone:registry=https://npm.pkg.github.com" > .npmrc
        ## I try to return it to the original form
        #echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" >> .npmrc
        ##echo "//npm.pkg.github.com/:_authToken=****************************************" >> .npmrc

    # The build creates the /dist directory
    # Probably "run build" needs access to private repo otherwise error "npm ERR! code E401" "npm ERR! 401 Unauthorized - GET" "authentication token not provided", solution is to create .npmrc
    # Error "Permission permission_denied: read_package", will https://github.com/orgs/circles-zone/packages/npm/logger-remote/settings solve it
    - name: Install dependencies and build (npm update)
      run: |
        #npm i
        npm update
      #env:
        # https://stackoverflow.com/questions/76143517/github-actions-not-passing-environment-variables-to-vite-build
        # Do we need it?
        #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        # Should the variable be called AWS_DEFAULT_REGION?
        #AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}

        #AWS_DEFAULT_REGION: ${{ vars.REGION }}
        #env for tests
        #VITE_ENVIRONMENT: MANG1
        #VITE_ENVIRONMENT_NAME: MANG1
        #ENVIRONMENT: MANG1
        #VITE_BRAND_NAME: Circlez
        #BRAND_NAME: ${{ inputs.brand-name }}
        #LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}

    # Some of the errors will be shown only if doing npx tsc (in case `npm i` is not executing npx tsc)
    - name: npx tsc
      run: |
        cd ./${{ inputs.repo-directory }}
        #tsc
        #npm run build
        npx tsc
       #env:
      # Reached heap limit Allocation failed - JavaScript heap out of memory
      # https://www.makeuseof.com/javascript-heap-out-of-memory-error-fix/
      # NODE_OPTIONS: --max-old-space-size=4096

    # Create /dist folder
    - name: npm run build
      run: |
        cd ./${{ inputs.repo-directory }}
        npm run build
        
    # - name: npm recursive
    #   run: |
    #     # Uncomment only in case the developer didn't install eslint-plugin locally on his computer
    #     # TypeScript
    #     #npm install eslint@latest --save-dev
    #     #npm install @typescript-eslint/eslint-plugin@latest --save-dev
    #     # React.js- Only when using react.js
    #     #npm install eslint-plugin-react-hooks@latest --save-dev          # If the developed didn't do it
    #     echo "Checking submodule contents"
    #     ls -la infrastructure
    #     git submodule update --init --recursive

    - name: Extract branch name
      shell: bash
      run: |
        # Working
        #echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}
        BRANCH_WITHOUT_UNDERSCORE=${ORIGINAL_BRANCH_NAME//_/-}
        BRANCH_WITHOUT_UNDERSCOPE_AND_AMPERSAND=${BRANCH_WITHOUT_UNDERSCORE//&/-}
        echo "branch=$BRANCH_WITHOUT_UNDERSCOPE_AND_AMPERSAND" >> $GITHUB_OUTPUT
        # TODO _ -> -
        # TODO & -> -
        # TODO branch_normalized max 63 characters
      id: extract_branch

    # Needs the branch name
    # Should run when we have a dist folder (Should done after the build which creates the /dist  directory)
    # Should do `npm run build` to create \dist (not created by `npx tsc`)
    - name: Create HelpAbout file
      run: |
        echo cat dist/HelpAbout/HelpAbout.txt
        cat dist/HelpAbout/HelpAbout.txt
        # Created in the root directory which is a public directory in the GitHub repo
        # TODO Rename the directory to help_about.txt like other directories
        echo "This is a great product" > dist/HelpAbout/HelpAbout.txt
        echo "Branch: ${{ steps.extract_branch.outputs.branch }}" >> dist/HelpAbout/HelpAbout.txt
        date >> dist/HelpAbout/HelpAbout.txt
        echo ${{ github.actor }} >> dist/HelpAbout/HelpAbout.txt
        echo cat dist/HelpAbout/HelpAbout.txt
        cat dist/HelpAbout/HelpAbout.txt

    # Works? Include in all three TypeScript templates?
    - name: npm run lint
      #if: ${{ contains(github.event.head_commit.message, 'smoke_test') }}
      run: |
        # Uncomment only in case the developer didn't install eslint-plugin locally on his computer
        # TypeScript
        #npm install eslint@latest --save-dev
        #npm install @typescript-eslint/eslint-plugin@latest --save-dev
        # React.js- Only when using react.js
        #npm install eslint-plugin-react-hooks@latest --save-dev          # If the developed didn't do it
        npm run lint

    - name: npm test
      #if: ${{ contains(github.event.head_commit.message, 'smoke_test') }}
      run: npm test --detectOpenHandles --coverage
      env:
        VITE_BRAND_NAME: ${{ inputs.brand-name }}
        VITE_ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        VITE_PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        VITE_PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # TODO Do we need it? - It seems not
        #VITE_REACTJS_ENVIRONMENT_NAME: ${{ inputs.environment-name }}

    - name: "npx vitest --coverage"
      #if: ${{ contains(github.event.head_commit.message, 'smoke_test') }}
      run: npx vitest --coverage
      env:
        # VITE_ environment variables for Vite React.js, without VITE_ for TypeScript (we must have both)
        VITE_BRAND_NAME: ${{ inputs.brand-name }}
        BRAND_NAME: ${{ inputs.brand-name }}
        VITE_ENVIRONMENT_NAME: ${{ inputs.environment-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}
        VITE_PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}
        VITE_PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}

    # TODO: Make sure we can see the report
    - name: Vitest Coverage Report
      #if: ${{ contains(github.event.head_commit.message, 'smoke_test') }}
      #if: always() # Also generate the report if tests are failing
      uses: davelosert/vitest-coverage-report-action@v2.1.1 # https://github.com/davelosert/vitest-coverage-report-action

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4.0.1 # https://github.com/aws-actions/configure-aws-credentials
      with:
        aws-access-key-id: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        aws-secret-access-key: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        aws-region: ${{ vars.AWS_DEFAULT_REGION }} # or your desired AWS region
      env:
        # Do we need all those?
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        # Should the variable be called AWS_DEFAULT_REGION?
        #AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}
        #env for tests
        #VITE_ENVIRONMENT: ${{ inputs.environment-name }}
        VITE_ENVIRONMENT_NAME: ${{ inputs.environment-name }}
        #ENVIRONMENT: ${{ inputs.environment-name }}
        VITE_BRAND_NAME: ${{ inputs.brand-name }}
        # Is it mandatory? - Trying to resolve a bug shown in the browser console
        BRAND_NAME: ${{ inputs.brand-name }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}

    - name: Deploy the React.js Application to the unsecured AWS S3 bucket branch folder ${{ inputs.frontend-s3-bucket-name }}.${{ inputs.environment-name }}.circ.zone
      if: github.ref != 'refs/heads/dev'
      run: |
          # AWS S3 Sync also to Play1 Unsecured S3 circlez-user-reactjs-frontend-unsecured-s3.play1.circ.zone
          #aws s3 sync ./dist s3://circlez-user-reactjs-frontend-unsecured-s3.play1.circ.zone --delete
          aws s3 sync ./dist s3://circlez-user-reactjs-frontend-unsecured-s3.${{ inputs.environment-name }}.circ.zone --delete
          # I tried to add " to solve the case of () in the branch name
          ##aws s3 sync ./dist "s3://circlez-user-reactjs-frontend-unsecured-s3.play1.circ.zone/${{ steps.extract_branch.outputs.branch }}" --delete

    - name: Deploy to the AWS S3 bucket branch folder ${{ inputs.frontend-s3-bucket-name }}.${{ inputs.environment-name }}.circ.zone
      if: github.ref != 'refs/heads/dev'
      run: |
        # We need to leave only one of them
        # I tried to create also with www.play1.circ.zone bucket name so http://www.play1.circ.zone will work
        # TODO We should create this bucket ahead of time
        # TODO We should find a better solution
        # We prefer the S3 Bucket name to include "frontend"
        #aws s3 sync ./dist s3://circles-user-reactjs.play1.circ.zone --delete
        #aws s3 sync ./dist s3://circlez-user-reactjs-frontend.${{ inputs.environment-name }}.circ.zone --delete
        #aws s3 sync ./dist s3://circles-user-reactjs-frontend.${{ inputs.environment-name }}.circ.zone --delete
        ##aws s3 sync ./dist s3://${{ inputs.frontend-s3-bucket-name }}.${{ inputs.environment-name }}.circ.zone --delete
        #aws s3 sync ./dist s3://www.${{ inputs.environment-name }}.circ.zone --delete

        # Copy to feature branch a subdomain
        repo=$(echo 'console.log("${{ steps.extract_branch.outputs.branch }}".toLowerCase())' | node -)
        aws s3 sync ./dist s3://${{ inputs.frontend-s3-bucket-name }}.${{ inputs.environment-name }}.circ.zone/branch/${repo} --delete
      env:
        # Do we need it?
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}

        # I think we can delete this
        #VITE_ENVIRONMENT_NAME: ${{ inputs.environment-name }}
        #VITE_BRAND_NAME: ${{ inputs.brand-name }}
