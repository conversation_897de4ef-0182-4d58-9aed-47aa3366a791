# TODO: Please replace the name with something like "Build & Deploy authentication-restapi-typescript-serverless-com to AWS play1"

# Examples:

## Local:
##   https://github.com/circles-zone/group-profile-local-restapi-typescript-serverless-com/blob/dev/.github/workflows/build_deploy_group_profile_typescript_serverless_com_play1.yml

# I think we need to move this link as not using serverless
##   https://github.com/circles-zone/database-mysql-local-typescript-package/edit/BU-2084-fixing-errors-in-crud/.github/workflows/build-publish-database-mysql-local-typescript-to-npm-github-package-play1.yml


name: Build & Deploy to AWS

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      #token:
        #required: false
        #type: string
        #TODO I didn't manage to have a default value from secrets
        #default: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
      repo-owner:
        required: false
        type: string
        default: circles-zone
      repo-name:
        required: false
        type: string
        default: ${{ github.repository }}
      branch-name:
        required: false
        type: string
        default: ${{ github.ref }}
      repo-directory:
        required: true
        type: string
        # TODO How can we use the value of repo-name as default?
        #default: 
      package-directory:
        required: false
        type: string
      debug:
        required: false
        type: string
        default: "0"
      is-rds-security-group:
        required: false
        type: boolean
        default: false
      component-name:
        required: false
        type: string
        default: 'No component name specified in the GitHub Action'
      #is-run-local:
        #required: false
        #type: boolean  
      #is-run-remote:
        #required: false
        #type: boolean
      is-install-serverless-domain-manager:
        required: false
        type: boolean
        default: false
      # Solve the Context access might be invalid
      aws_sg_name:
        required: false
        type: string
        #default: ''
      default_aws_region:
        required: false
        type: string
      RDS_USERNAME:
        required: false
        type: string

jobs:
  build_deploy:
    # Name should be similar to the file name
    name: Deploy TypeScript Serverless.com (Build, Test & Deploy)
    if: ${{contains(github.event.head_commit.message, '[pub]') }}
    runs-on: ubuntu-latest
    # group-main-local-restapi-typescript-serverless-com 5 min is not enough
    timeout-minutes: 10
    environment: 
      name: ${{ inputs.environment-name }} # play1 # If using $environment-name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ inputs.environment-name }}.circ.zone
    strategy:
      matrix:
        # TODO Can we change it to 20.x?
        node-version: [20.x] # I changed node-version from 14.x to 16.x, per warning. eslint 8 support 16.x+
    steps:
    - uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
        repository: ${{ inputs.repo-owner }}/${{ inputs.repo-name }}
        ref: ${{ inputs.branch-name }}
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }} # Only to access another repo, i.e. https://github.com/circles-zone/logger-local-typescript-package

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@main # v4.0.1 # https://github.com/actions/setup-node
      with:
        node-version: ${{ matrix.node-version }}
        # TODO Do we need it?
        registry-url: "https://npm.pkg.github.com"

    # TODO Maybe we should create .npmrc here
    
    # No need to create .npmrc before npm ci
    # In some cases it is needed to run both "npm i" and "npm ci" (i.e. marketplace-goods-graphql-typescript-serverless-com)
    # In the development environment it is better to run `npm i` and not `npm ci`
    # If you have "Code E401" / "401 unauthenticated: User cannot be authenticated with the token provided." error please make sure you have the updated read token in the .npmrc file and pushed it to the right directory in the repo in Git. Make sure we have only one authToken in the .npmrc file in GitHub Repo and it is valid for reading packages.
    # If you have "Code E403", make sure there is .npmrc file in the relevant directory
    # If you have "Code E403", give permission the other repo in https://github.com/orgs/circles-zone/packages/npm/logger-local/settings for example
    # If we get "Permission denied" error check .npmrc in the repo "_authToken=****************************************"
    - name: npm update
      run: |
        echo "pwd"
        pwd
        echo "ls -lag"
        ls -lag

        # Probably this version works for authentication-local-rest-typescript-serverless-com
        echo "cd ./${{ inputs.repo-directory }}"
        cd ./${{ inputs.repo-directory }}
        echo "pwd"
        pwd
        echo "ls -lag"
        ls -lag

        #echo "cd ./${{ inputs.repo-name }}" # or cd ./${{ inputs.repo-directory }}
        #cd ./${{ inputs.repo-name }}
        #echo "pwd"
        #pwd
        #echo "ls -lag"
        #ls -lag
        
        # Trying to support logger-restapi-typescript-serverless-com with authentication-local-rest-typescript-serverless-com as Sanity Test
        #cd ~/work/${{ inputs.repo-name }}
        #echo "pwd"
        #pwd
        #echo "ls -lag"
        #ls -lag

        #cd ~/work/${{ inputs.repo-name }}/${{ inputs.repo-directory }}
        #echo "pwd"
        #pwd
        #echo "ls -lag"
        #ls -lag

        echo "Running `npm i` ..."
        # npm WARN using --force Recommended protections disabled.
        #npm i --force
        npm i
        echo "Running `npm update` ..."
        npm update

        if ${{ inputs.environment-name == 'play1' }}; then
          echo "In play1 need to npm update"
        else
          echo "In all the rest of the environments, need to npm i"
        fi

    # Some of the errors will be shown only if doing npx tsc (in case `npm i` is not executing npx tsc)
    - name: npx tsc
      run: |
        cd ${{ inputs.repo-directory }}
        npx tsc
      #env:
        # Reached heap limit Allocation failed - JavaScript heap out of memory
        # https://www.makeuseof.com/javascript-heap-out-of-memory-error-fix/
        #NODE_OPTIONS: --max-old-space-size=4096

    # Works? Include in all three TypeScript templates?
    - name: npm run lint
      run: |
         cd ${{ inputs.repo-directory }}/${{ inputs.package-directory }}

         # TODO Uncommented for https://github.com/circles-zone/logger-restapi-typescript-serverless-com, should be commented
         # We need those npm installs in case the developer didn't install eslint locally before running GHA
         # Uncommenting those like
         #npm install -D typescript
         #npm install eslint@latest --save-dev
         #npm install @typescript-eslint/eslint-plugin --save-dev
         #npm install eslint-plugin-react-hooks@latest --save-dev           # Only for React.js

         # https://stackoverflow.com/questions/77848891/how-can-i-fix-unable-to-resolve-dependency-tree-while-installing-eslint
         #npm i --legacy-peer-deps

         npm run lint

    - name: Get GitHub Action (GHA) runner IP
      if: inputs.is-rds-security-group == true
      id: ip
      uses: haythem/public-ip@master # There is no main branch # v1.3
      with:
        maxRetries: 80 # 5 -> 40 -> 80

    - name: Setting DEFAULT_AWS_REGION and AWS_SG_NAME environment variables.
      if: inputs.is-rds-security-group == true
      run: |
        # 
        #echo "DEFAULT_AWS_REGION=us-east-1" >> $GITHUB_ENV
        # inputs.DEFAULT_AWS_REGION
        
        # EC2 Security Group of RDS/MySQL
        echo "AWS_SG_NAME=mysql_mang_sg" >> $GITHUB_ENV

    - name: Add GitHub Actions (GHA) runner IP to MySQL EC2 Security Group
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 authorize-security-group-ingress --group-name ${{ inputs.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32    
      env:
        # As the RDS/MySQL in the Management/Master AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        # TODO I think we should rename it to DEFAULT_AWS_REGION
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}

    - name: Set environment variables
      run: |
        if ${{ inputs.repo-name == 'storage-graphql-server-typescript-serverless-com' }} || ${{ inputs.repo-name == 'xxx' }} ; then
          echo "Setup AWS_DEFAULT_REGION Environment Variables"
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          # AWS_DEFAULT_STORAGE_BUCKET_NAME
          echo "AWS_DEFAULT_STORAGE_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a AWS_DEFAULT_REGION repo"
        fi

        # TODO delete
        if ${{ inputs.repo-name == 'opensearch-local-python-package' ||
          inputs.repo-name == 'event-main-local-restapi-python-package-serverless-com' }}; then
          echo "Setup OPENSEARCH Environment Variables"
          echo "OPENSEARCH_HOST=${{ vars[format('OPENSEARCH_HOST_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "OPENSEARCH_INITIAL_ADMIN_PASSWORD=${{ secrets[format('OPENSEARCH_INITIAL_ADMIN_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not OPENSEARCH repo"
        fi

        
    # Should we use "npm test --runInBand"?
    - name: npm test
      run: |
        cd ${{ inputs.repo-directory }}
        # If we get "jest: not found" error, uncomment the below line
        npm install -g jest
        # https://stackoverflow.com/questions/55991641/npm-test-coverage-never-exits
        # I added '-- coverage'
        npm test --detectOpenHandles --coverage
      env:
        # TODO: Please comment and then delete all variables which are not being used ENVIRONMENT, LOGZIO_TOKEN are mandatory)
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }} # For CORS
        
        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        # TODO: This variable is temporarily used by Logger TypeScript, it should be changed with the new DebugMode variable LOGGER_MINIMUM_SEVERITY and .logger.json file
        DEBUG: ${{ inputs.debug }} # 1
        # As of now, this variable is not in use yet in Logger TypeScript 
        #LOGGER_MINIMUM_SEVERITY: Warning

        # This is mandatory for services such as authentication-local-rest-typescript-serverless-com
        # TODO Comment and then delete JWT_SECRET_KEY
        JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }} # If we need to access our APIs
        USER_JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }} # If we need to access our APIs

        # Database (RDS) credentials must be here as we need them for all deployable serverless.com, test with SANITY_RDS_USERNAME, and deployment with RDS_USERNAME
        RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        # TODO We prefer to use SANITY_RDS_USERNAME i.e. storage-graphql-server-typescript-serverless-com
        # RDS_USERNAME: ${{ SANITY_RDS_USERNAME }}
        RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}
        # RDS_PASSWORD: ${{ secrets[format('RDS_PASSWORD_{0}', inputs.environment-name)] }}
        RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}
        
        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}

        # Error resolved "ConfigError: Missing region in config"
        # Request.VALIDATE_REGION (node_modules/aws-sdk/lib/event_listeners.js:128:47)
        # TODO Which one shall we use?
        # Needed for storage-graphql-server-typescript-serverless-com (TODO Shall we add set of Set Environment Variables)
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}
        #DEFAULT_AWS_REGION: ${{ vars.AWS_DEFAULT_REGION }}

        # Needed for storage-graphql-server-typescript-serverless-com
        AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}

        # Cognito used by: user-registration-local-restapi-typescript-serverless-com
        #USER_POOL_ID: ${{ vars[format('USER_POOL_ID_{0}', inputs.environment-name)] }}
        # Error: "MissingRequiredParameter: Missing required key 'ClientId' in params"
        #USER_POOL_WEB_CLIENT_ID: ${{ secrets[format('USER_POOL_WEB_CLIENT_ID_{0}', inputs.environment-name)] }}

        # Used by: user-registration-local-restapi-typescript-serverless-com
        #QUEUE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.QUEUE_GRAPHQL_APPSYNC_API_KEY }}
        #QUEUE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets[format('QUEUE_GRAPHQL_APPSYNC_API_KEY_{0}', inputs.environment-name)] }}

    - name: Remove Github Actions (GHA) runner IP from RDS/MySQL EC2 Security Group
      #if: always()
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 revoke-security-group-ingress --group-name ${{ inputs.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # As the RDS/MySQL in the Management/Master AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        DEFAULT_AWS_REGION: ${{ inputs.DEFAULT_AWS_REGION }}

  # I commented this as separating into two jobs, requiring checkout and setup-node again
  #deploy:
    #name: deploy
    #needs: build
    #runs-on: ubuntu-latest
    # Is it mandatory?
    #environment: dvlp1 # If using $environment-name, GitHub Environment Secrets are not deployed in Lambda Function Environment    
    #steps:

    # Needed both for "serverless plugin install -n serverless-domain-manager" and "serverless doctor "
    - name: Serverless installation
      run: |
        echo 1
        #npm install -g serverless
        #I think this resolved `Cannot parse "serverless.ts": Resolution of "ts-node" failed with: "ts-node" not found` error in serverless/github-action@master
        #npm install -g serverless@3.38.0
        npm install -g serverless@3.39.0
        serverless --version


    - name: Serverless plugin install -n serverless-domain-manager (only if not run locally)- Needed only for group-profile-local-restapi-typescript-serverless-com
      if: inputs.is-install-serverless-domain-manager == true
      run: |
        echo 2 cd as serverless command should be executed in the directory of the configuration
        #cd ${{ inputs.repo-directory }}/${{ inputs.package-directory }}
        # Changed for group-main-local-restapi-typescript-serverless-com as the configuration in the second level
        cd ${{ inputs.repo-directory }}
        #pwd
        #ls -lag
        echo 3 serverless plugin install -n serverless-domain-manager
        # works with group-main serverless.ts do not work with logger serverless.yml
        serverless plugin install -n serverless-domain-manager
        echo 4 npm install -g ts-node - Resolve Cannot parse "serverless.ts": Resolution of "ts-node" failed with: "ts-node" not found error in serverless/github-action@master 
        # resolve `Cannot parse "serverless.ts": Resolution of "ts-node" failed with: "ts-node" not found` error in serverless/github-action@master
        # Maybe I need to move this to the previous step
        npm install -g ts-node

    # When we switch to Serverless.com Framework v4 we need to do serverless login before serverless/github-action@master
    # TODO Do we need this step?
    # Commented as it is interactive
    #- name: serverless login
      #run: |
        #serverless login
      #env:
        # This is needed for Serverless.com V4 hopefully to resolved the serverless login
        #SERVERLESS_ACCESS_KEY: ${{ secrets[format('SERVERLESS_ACCESS_KEY_{0}', inputs.environment-name)] }}


    # Might need to run `serverless plugin install -n serverless-domain-manager` locally
    # Deploy Serverless.com in AWS CloudFormation Stack, API Gateway, Lambda Functions
    # When we switch to Serverless.com Framework v4 we need to do serverless login
    - name: Deploy Serverless.com in AWS CloudFormation Stack, API Gateway, Lambda Functions (new from github-workflows)
      #uses: serverless/github-action@master # v3.2.0 # https://github.com/serverless/github-action
      uses: serverless/github-action@v4 # v4 # https://github.com/serverless/github-action
      with:
        # Can't change the directory, it should be the directory of serverless.ts
        #args: -c "cd ${{ inputs.repo-directory }} && serverless plugin install --name serverless-offline && serverless deploy --org=circles --stage ${{ inputs.environment-name }} --region us-east-1"
        #args: -c "cd ${{ inputs.repo-directory }} && pwd && ls -lag && serverless plugin install --name serverless-offline@13.7.0 -- force && serverless deploy --org=circles --stage ${{ inputs.environment-name }} --region us-east-1"
        # https://github.com/zirkelc/serverless-esm-ts?tab=readme-ov-file
        #args: -c "cd ${{ inputs.repo-directory }} && pwd && ls -lag && serverless plugin install --name serverless-offline@13.7.0 -- force && serverless deploy --config serverless.cjs --org=circles --stage ${{ inputs.environment-name }} --region us-east-1"
        # Adding serverless login as authentication-local-restapi moved to Serverless Framework V4
        # Removed && echo "serverless login" && serverless login
        # Removed && echo "serverless plugin..."
        # Removed && echo "serverless deploy"
        # Removed serverless plugin install .. --force
        args: -c "cd ${{ inputs.repo-directory }} && pwd && ls -lag && serverless plugin install --name serverless-offline && serverless deploy --config serverless.ts --org=circlez --stage ${{ inputs.environment-name }} --region us-east-1 --debug"
        entrypoint: /bin/sh
      env:
        # This list should be in sync with the list in serverless.ts (less preferred serverless.yml)
        # TODO We need to test if it is working
        # Since this is Serverless.com deployment we prefer not to use our fixed PRODUCT_USER_IDENTIFIER AND PRODUCT_PASSWORD 
        # TODO: Please comment and then delete all variables not being used.
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }} # For CORS

        # TODO I'm not sure AWS_ needed if we use SERVERLESS_ACCESS_KEY in Serverless.com V4 (it is needed in Serverless.com V3)
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}

        # TODO Can we comment and then delete the below line
        JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }}
        USER_JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        #DEBUG: 1
        # TODO: This variable is temporarily used by Logger TypeScript, it should be changed with the new DebugMode variable LOGGER_MINIMUM_SEVERITY and .logger.json file
        DEBUG: ${{ inputs.debug }} # 1

        # Database (RDS) credentials must be here as we need them for all deployable serverless.com, test with SANITY_RDS_USERNAME and deployment with RDS_USERNAME
        RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

        # Cognito used by: user-registration-local-restapi-typescript-serverless-com
        #USER_POOL_ID: ${{ secrets[format('USER_POOL_ID_{0}', inputs.environment-name)] }}
        # Error: "MissingRequiredParameter: Missing required key 'ClientId' in params"
        #USER_POOL_WEB_CLIENT_ID: ${{ secrets[format('USER_POOL_WEB_CLIENT_ID_{0}', inputs.environment-name)] }}

        # Used by: user-registration-local-restapi-typescript-serverless-com
        #QUEUE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.QUEUE_GRAPHQL_APPSYNC_API_KEY }}

        # This is needed for Serverless.com V4 hopefully to resolved the serverless login
        SERVERLESS_ACCESS_KEY: ${{ secrets[format('SERVERLESS_ACCESS_KEY_{0}', inputs.environment-name)] }}

    - name: Serverless doctor
      run: |
        echo "pwd"
        pwd
        echo "ls -lag"
        ls -lag

        # To make it work with logger-rest-typescript-serverless-com work with authentication-local-restapi-typescript-serverless-com (as sanity test)
        cd ${{ inputs.repo-directory }}

        echo "serverless doctor option 1"
        serverless doctor

        #echo "cd ~/work"
        #cd ~/work
        #pwd
        #echo "ls -lag"
        #ls -lag

        #echo "cd ~/work/${{ inputs.repo-name }}"
        #cd ~/work/${{ inputs.repo-name }}
        #pwd
        #echo "ls -lag"
        #ls -lag

        #echo "cd ~/work/${{ inputs.repo-name }}/${{ inputs.repo-name }}"
        #cd ~/work/${{ inputs.repo-name }}/${{ inputs.repo-name }}
        #pwd
        #echo "ls -lag"
        #ls -lag

        # To make it work with logger-rest-typescript-serverless-com work with authentication-local-restapi-typescript-serverless-com (as sanity test)
        # TODO Not working when logger-restapi-typescript-serverless-com calls auth-localrest-typescript-serverless.com
        # Note that repo-name should be of the original repo
        #echo "cd ~/work/${{ inputs.repo-name }}/${{ inputs.repo-name }}/${{ inputs.repo-directory }}"
        #cd ~/work/${{ inputs.repo-name }}/${{ inputs.repo-name }}/${{ inputs.repo-directory }}
        #pwd
        #echo "ls -lag"
        #ls -lag

        # I'm afraid we can't upgrade to the latest version, which we have issues with. 
        # Need `npm install -g serverless@3.38.0`
        #echo "serverless doctor option 2"
        #serverless doctor
      env:
        #SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
        SERVERLESS_ACCESS_KEY: ${{ secrets[format('SERVERLESS_ACCESS_KEY_{0}', inputs.environment-name)] }}


    - name: TODO Update endpoint URL in url-remote-python and url-remote-typescript manually
      run: |
        echo "TODO Write powershell script that make sure that we update the endpoint URL in both url-remote-python and url-remote-typescript. Make sure we run this script from both deploy_typescript_serverless_com and deploy_python_serverless_com GHA yml"


    - name: TODO Update endpoint URL in GCP Project Auth 2.0 manually (and GHA Reusable workflows)
      run: |
        echo "TODO Write script that in the case of google-account-restapi-python-serverless-com repo. If needed, it also update the endpoint URL in the relevant GCP Project Auth 2.0 Authorized URI (and if needed also in the Reusable GHA workflows) 1. console.cloud.google.com 2. APIs and services 3. Credentials 4. OAuth 2.0 Client IDs 5. add/update the Authorised redirect URIs"
