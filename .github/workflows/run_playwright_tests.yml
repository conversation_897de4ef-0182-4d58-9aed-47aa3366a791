# Used in:
# https://github.com/circles-zone/authentication-remote-typescript-package/edit/BU-2189-Insert-tenant-form-process.env-in-the-url-adress-<PERSON><PERSON>-<PERSON><PERSON>/.github/workflows/build_publish_authentication_remote_typescript_package_github_npm_play1.yml
# https://github.com/circles-zone/circles-user-reactjs-frontend/blob/dev/.github/workflows/build_deploy_circles_user_reactjs_frontend_to_aws_s3_play1.yml

name: Run UX/UI Playwright Tests

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: false
        type: string
        default: circles-user-reactjs-frontend
      # Current branch is: ${GITHUB_REF##*/}
      branch-name:
        required: false
        type: string
        # Not good as this is the branch name of the original repo
        #default: ${{ github.ref_name }}
        default: dev
      repo-directory:
        required: false
        type: string
        default: .
      package-directory:
        required: false
        type: string
        default: .
      is-rds-security-group:
        required: false
        type: boolean
      component-name:
        required: false
        type: string
      is-run-local:
        required: false
        type: boolean    
      is-run-remote:
        required: false
        type: boolean
      is-run-playwright:
        required: false
        type: boolean
        default: false

jobs:        
  run_playwright_tests:
    name: Run Playwright Tests
    # Do not run Playwrite E2E Tests in play1 as it takes a lot of time and the developer is committed to the Playwright tests passed locally, we run the Playwright tests only in dvlp1
    #if: inputs.is-run-playwright == true # || inputs.is-deploy-in-domain-root == true
    #if: github.event.pull_request.draft == false
    #if: inputs.is_run_playwright == 'true'
    # Commented as this might blocking it from running
    #if: ${{ github.event.pull_request.draft == 'false' && inputs.is_run_playwright == 'true' }}
    if: ${{contains(github.event.head_commit.message, '[pub]') }}

    # TODO Reduce to actual in the job workflow
    # 29/3/2024 Circles React.js 20 minutes is not enough, it took 37 minutes, so I changed from 40 to 50 minutes
    timeout-minutes: 50 # 2 not enough, took 13 minutes, 17 minutes not enough

    runs-on: ubuntu-latest
    strategy:
      # Trying to block more than one instance running in parallel
      max-parallel: 1
      matrix:
        #node-version: [20.x]
        node-version: [20]
    steps:
      - uses: actions/checkout@main # v4.1.1 https://github.com/actions/checkout
        # If we'll use inputs: "inputs.is-run-playwright == true ||"
        #if: ${{ inputs.is-run-playwright == 'true' || inputs.is-deploy-in-domain-root == 'true' }}
        with:
          submodules: true # To use the email_validator ...
          # TODO Shall we change the secret to start with GH_ like others
          # Maybe not working
          #token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
          token: ${{ secrets.REACTJS_SUBMODULES_GITHUB_TOKEN }}
          repository: circles-zone/${{ inputs.repo-name }} # Not mandatory
          # TODO can we conditionally add this line?
          ref: ${{ inputs.branch-name }}

      - uses: actions/setup-node@main # v4.0.0 https://github.com/actions/setup-node
        #if: ${{ inputs.is-run-playwright == 'true' || inputs.is-deploy-in-domain-root == 'true' }}
        with:
          #node-version: 20
          node-version: ${{ matrix.node-version }}

      - name: npm update
        #if: ${{ inputs.is-run-playwright == 'true' || inputs.is-deploy-in-domain-root == 'true' }}
        run: |
          npm update

      # Error "error TS2307: Cannot find module '' or its corresponding type declarations. ERROR: "_tsc" exited with 2."
      # Resolved using checkout with submodules: or token:
      - name: npm run build
        #if: ${{ inputs.is-run-playwright == 'true' || inputs.is-deploy-in-domain-root == 'true' }}
        run: |
          cd ${{ inputs.repo-directory }}
          npm run build

      # TODO `npx tsc` is similar to `npm run build`, do we need both? 
      - name: npx tsc
        #if: ${{ inputs.is-run-playwright == 'true' || inputs.is-deploy-in-domain-root == 'true' }}
        run: |
          cd ${{ inputs.repo-directory }}
          npx tsc

      - name: Install Playwright Browsers
        if: ${{ inputs.is-run-playwright == 'true' }}
        run: |
          # Not mandatory
          # https://github.com/microsoft/playwright/issues/23387
          #npm uninstall @playwright/test
          #npm install @playwright/test@1.33

          # Mandatory
          npx playwright install --with-deps

      - name: Run Playwright tests
        if: ${{ inputs.is-run-playwright == 'true' }}
        run: |
          #cd ${{ inputs.repo-directory }}
          npx playwright test --reporter=junit
          #echo "ONLY npx playwright test menu.spec.ts"
          #npx playwright test menu.spec.ts
        env:
          VITE_BRAND_NAME: ${{ inputs.brand-name }}
          BRAND_NAME: ${{ inputs.brand-name }}
          VITE_ENVIRONMENT_NAME: ${{ inputs.environment-name }}
          ENVIRONMENT_NAME: ${{ inputs.environment-name }}

          #VITE_REACTJS_ENVIRONMENT_NAME: ${{ inputs.environment-name }}
          VITE_REACTJS_ENVIRONMENT_NAME: 'play1-s3-unsecured'

          #brand-name: ${{ inputs.brand-name }}

          # TODO We should delete this line
          VITE_PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
          VITE_PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

# TODO Push the junit.xml to Xray (To see coverage)
# TODO Allure - local HTML file, no history
# TODO ReportPortal- Machine, with history