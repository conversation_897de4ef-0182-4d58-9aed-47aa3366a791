# Used in
#   https://github.com/circles-zone/event-local-restapi-python-serverless-com/edit/dev/.github/workflows/build_deploy_event_local_restapi_python_serverless_com_play1.yml#L348C1-L348C1
#   https://github.com/circles-zone/group-local-restapi-typescript-serverless-com/edit/BU-2027-editing-methods-to-handle-empty-responses-Yarden-Dali%5D/.github/workflows/build_deploy_group_local_restapi_typescript_serverless_com_play1.yml

# Run without publish
name: Run TypeScript Package

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: true
        type: string
      branch-name:
        required: false
        type: string
        default: dev
      repo-directory:
        required: true
        type: string
      package-directory:
        required: false
        type: string
        default: .
      is-rds-security-group:
        required: false
        type: string
        default: false
      #component-name:
        #required: true
        #type: string
      is-run-local:
        required: false
        type: string    
      is-run-remote:
        required: false
        type: string
      #SQL_USERNAME:
        #required: false # required in local, not required in remote
        #type: string
      logger-is-write-to-sql:
        required: false
        type: string
        default: false

jobs:        
  # Run remote-package TypeScript to test the deployment (We should run all Remote packages)
  run_typescript_package:
    name: Run TypeScript Package
    #if: inputs.is_run_auth_remote == 'true'
    runs-on: ubuntu-latest
    #env:
      ## TODO Should be identical to the repo name
      #repo-name: authentication-remote
    environment:
      name: ${{ inputs.environment-name }} # play1 # If using $environment-name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ inputs.environment-name }}.circ.zone
    strategy:
      matrix:
        node-version: [20.x] # I changed node-version from 14.x to 16.x, per warning
    steps:
    - uses: actions/checkout@main # v4.1.1 https://github.com/actions/checkout
      with:
        # Repository name with owner. For example, actions/checkout
        # Default: ${{ github.repository }}
        repository: circles-zone/${{ inputs.repo-name }}
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
        ref: ${{ inputs.branch-name }}

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@main # v4.0.0 https://github.com/actions/setup-node
      with:
        node-version: ${{ matrix.node-version }}

    # Create .npmrc in case it doesn't exist or the phips28/gh-action-bump-version replaced the authToken
    # Needed for `npm update` or `npm i`
    # Can we delete secrets.GIT_HUB_PACKAGE_WRITE_TOKEN?
    # To avoid the error "unauthenticated: User cannot be authenticated with the token provided." while doing `npm update`
    - name: Create .npmrc for `npm update` or `npm i` to allow accessing our private repos in GPR
      run: |
        cd ./${{ inputs.repo-directory }}
        # To solve "code E403 npm ERR! 403 403 Forbidden - PUT https://npm.pkg.github.com/circles-zone/@circles-zone%2fdatabase-typeorm-local - Permission permission_denied: The token provided does not match expected scopes.
        echo "@circles-zone:registry=https://npm.pkg.github.com" > .npmrc
        # secrets.GITHUB_TOKEN didn't work error "Permission permission_denied: read_package", so I changed it to GH_READ_REPO_TOKEN
        echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_CLASSIC_READ_PACKAGES_TOKEN }}" >> .npmrc

    # `npm i` install jest for `npm test`
    # As this is only to run remote `npm i` is enough
    - name: npm i to install jest
      #if: inputs.is_run_remote == 'true'
      run: |
        cd ./${{ inputs.repo-directory }}
        #npm update
        # `npm update` in play1 and `npm i` in dvlp1
        npm i

    # Should we use "npm test --runInBand"?
    # Remote also needs access to the database to get_test_entity_id()
    - name: npm test
      #if: inputs.is_run_local == 'true'
      run: |
        cd ./${{ inputs.repo-directory }}
        # https://stackoverflow.com/questions/55991641/npm-test-coverage-never-exits
        # I added '-- coverage'
        # Run all tests
        npm test --detectOpenHandles --coverage
        # Run only one `login success` test
        #npm test -- -t 'login success' --verbose
      env:
        # TODO: Please first comment and then delete all variables which are not being used.
        # TODO: In case of remote-typescript-package we need only brand-name and environment-name
        # TODO Check if we want to copy from storage-local
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        DEBUG: 1
        LOGGER_IS_WRITE_TO_SQL: ${{ inputs.logger-is-write-to-sql }}

        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # Used in local, and remote needed only because of testing to get_test_entity_id()
        RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
        RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}
        RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}  

        # TODO Should be included only if is_run_local
        #USER_JWT_SECRET_KEY: ${{ inputs.is_run_remote == 'true' && secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] || '' }}
        USER_JWT_SECRET_KEY: ${{ inputs.is_run_remote == 'true' && secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] || '' }}

        # TODO Change to QUEUE_GRAPHQL_APPSYNC_API_KEY
        #QUEUE_API_KEY: ${{ secrets.QUEUE_API_KEY }}
        QUEUE_API_KEY: ${{ secrets[format('QUEUE_GRAPHQL_APPSYNC_API_KEY_{0}', inputs.environment-name)] }}
        # location-graphql-remote-typescript
        # APP_SYNC -> APPSYNC
        # TODO Do we need || ''
        LOCATION_GRAPHQL_APPSYNC_API_KEY: ${{ secrets[format('LOCATION_GRAPHQL_APPSYNC_API_KEY_{0}', inputs.environment-name)] }}
