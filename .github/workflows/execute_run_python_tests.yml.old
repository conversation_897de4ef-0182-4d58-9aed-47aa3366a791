# We isolated the run_python_tests.yml step to check it

name: Build Publish External User Local Python Package to PyPI.org play1
on:
  push:
    # TODO remove dev
    branches: [ "BU-*", dev ]
  #pull_request:
   #branches: [dev]

env:
  brand_name: Circlez
  environment_name: play1
  repo_name: user-external-main-local-python-package

jobs:
  deploy:
    if: false
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone
    strategy:
      #fail-fast: false
      matrix:
        #python-version: ["3.12"] # We changed from python-version 3.x (which was 3.12) to 3.10 so we can use torch in requirements.txt in a Python Package
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        #poetry-version: ["2.1.6"]
    steps:
      - uses: actions/checkout@v4.1.0 # https://github.com/actions/checkout

      - name: Set up Python
        uses: actions/setup-python@v4.7.1 # https://github.com/actions/setup-python
        with:
          #python-version: "3.x"
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics   
      - name: Build package
        run: |
          cd ./$repo_name
          pip install build
          python -m build
      - name: Run Tests
        run: pytest
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}

          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}

      # TODO: Please create pytest-coverage.txt and pytest.xml
      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?


  # Shall we run it instead of the previous build step (it also part of the run_python_package, we tried to take it out)
  run-python-tests:
    name: Run Python Tests (w/o build) trying to use google-user
    # TODO uncomment
    #if: startsWith(github.ref, 'refs/heads/bu-')
    #if: ${{ true }}
    # TODO Please comment
    #if: false
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # No need to run the tests twice
    #if: github.ref=='refs/heads/dev'
    # Disable until we fix it
    #if: false
    # Let's try to run it in parallel to the main job
    #needs: run_remote_python
    #strategy:
      #matrix:
        # TODO We didn't manage to have multiple environments
        #target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: 
      repo-name: google-contact-local-python-package
      repo-directory: google-contact-local-python-package
      branch-name: dev
      #TODO We should bring it back to dev ASAP
      #branch-name: dev
      #package_directory_name: google-contact_local
      #is_rds_security_group: false
      #is_publish_to_non_test_pypi: true
      # TODO Make is based on Environment Name
      google-user: <EMAIL>
      #logger-minimum-severity: ${{ inputs.logger-minimum-severity }}