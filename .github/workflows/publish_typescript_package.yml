# Template
#   https://github.com/circles-zone/typescript-package-template/blob/dev/.github/workflows/build_publish_project_name_typescript_github_npm_package_play1.yml

# One Level (Works be I think we should move to two levels directory structure as we have in Python)
# Local Package used by
#   https://github.com/circles-zone/typescript-sdk-local-typescript-package/blob/dev/.github/workflows/publish-npm-github-package_play1.yml
#   https://github.com/circles-zone/profile-main-typeorm-local-typescript-package/blob/BU-2058-develop-profile-local-typeorm-typescript/.github/workflows/build-publish-project-name-typescript-github-npm-package-play1.yml
# Remote Package used by
#   https://github.com/circles-zone/authentication-remote-typescript-package/blob/BU-2189-Insert-tenant-form-process.env-in-the-url-adress-Or<PERSON>-<PERSON><PERSON>/.github/workflows/build_publish_authentication_remote_typescript_package_github_npm_play1.yml

# Two Levels (Not working as Reusable workflow supports only one level)
# Local
#   https://github.com/circles-zone/typescript-sdk-local-typescript-package/blob/dev/.github/workflows/publish-npm-github-package_play1.yml

name: Reusable workflow example

on:
  workflow_call:
    inputs:
      # TODO Can we use a variable?
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: true
        type: string
      # TODO Can we use a variable?
      branch-name:
        required: false
        type: string
        default: ${{ github.ref }}
        description: "Mostly we use the default branch name"
      repo-directory:
        required: true
        type: string
      package-directory:
        required: false
        type: string
        default: .
      is-rds-security-group:
        required: false
        type: boolean
        default: false
      component-name:
        required: false
        type: string
        default: 'No component name specified in the GitHub Action'
      is-bump-version:
        required: false
        type: boolean
        default: false
      is-run-local:
        required: false
        type: boolean
        default: false
      is-run-remote:
        required: false
        type: boolean
        default: false
      is-debug-mode:
        required: false
        type: boolean
        default: false
      #logger-minimum-severity
      #logger-is-write-to-sql

    #secrets:
      #RDS_PASSWORD:
        #required: true
    # Map the workflow outputs to job outputs
    #outputs:
      #firstword:
        #description: "The first output string"
        #value: ${{ jobs.example_job.outputs.output1 }}
      #secondword:
        #description: "The second output string"
        #value: ${{ jobs.example_job.outputs.output2 }}

permissions:
  #contents: read # read is the default if there are no permissions: . Needed for actions/checkout
  contents: write # Needed for phips28/gh-action-bump-version
  # If we have a separate job for publishing then "packages: read" is enough
  packages: 'write' # Write needed for the publish step. If the publish is in the job "write" otherwise "read".
  #pull-requests: write # Due to warning
  id-token: 'write'

jobs:

  set_environment_depricated:
    if: ${{contains(github.event.head_commit.message, '[pub]') }}
    outputs:
      my_env: ${{ steps.setenv.outputs.my_env }}
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
    - id: setenv
      run: echo "::set-output name=my_env::production"
  
  #pre_build_publish:
    #name: Build & Publish
    #runs-on: ubuntu-latest
    #outputs:
      #output1: ${{ steps.step1.outputs.firstword }}
      #output2: ${{ steps.step2.outputs.secondword }}
    #steps:
    #- uses: actions/labeler@v4
      #with:
        #repo-token: ${{ secrets.token }}
        #configuration-path: ${{ inputs.config-path }}
    #- id: step1
      #run: echo "firstword=hello" >> $GITHUB_OUTPUT
    #- id: step2
      #run: echo "secondword=world" >> $GITHUB_OUTPUT
    #- name: step 2
      #run: |
       #echo "Step 2"

  build_publish:
    # This name apears under the job running - Should start similar to the yml filename
    name: Publish TypeScript Package ${{ inputs.repo-name }}
    # TODO resolve the if: in publish-gpr and then uncomment this
    #if: "!contains(github.event.commits[0].message, '[pub]')"
    if: ${{contains(github.event.head_commit.message, '[pub]') }}
    runs-on: ubuntu-latest
    environment:
      name: ${{ inputs.environment-name }}
      # dev.circ.zone
      #url: http://${{ github.ref_name }}.circ.zone
      #url: http://${{ steps.set_environment_new.outputs.environment_url }}.circ.zone
      url: http://${{ inputs.environment-name }}.circ.zone

    permissions:
      #contents: read # read is the default if there are no permissions: . Needed for actions/checkout
      contents: write # Needed for phips28/gh-action-bump-version
      # If we have a separate job for publishing then "packages: read" is enough
      packages: write # Write needed for the publish step. If the publish is in the job "write" otherwise "read".
      #pull-requests: write # Due to warning
    strategy:
      matrix:
        node-version: [20.x]
      # Trying to allow multi-environment
      # Error: "FailFast: canceling since parallel instance has failed"
      # While debugging it is better to use false
      #fail-fast: false
    steps:

    - name: set_environment_new
      run: |
        echo "environment_url=http://$(environment-name)" >> $GITHUB_OUTPUT

    - uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
          # Repository name with owner. For example, actions/checkout
          # Default: ${{ github.repository }}
          repository: circles-zone/${{ inputs.repo-name }}
          # We might to regenerate (renew) Fine Grained GitHub Token https://github.com/settings/personal-access-tokens/2407943
          token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
          ref: ${{ inputs.branch-name }}

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@main # v4.0.1 # https://github.com/actions/setup-node
      with:
        #node-version: [20.x]
        node-version: ${{ matrix.node-version }}
        # Is it mandatory?
        #registry-url: 'https://npm.pkg.github.com'

    # Create .npmrc in case it doesn't exist or the phips28/gh-action-bump-version replaced the authToken
    # Needed for `npm update` or `npm i`
    # Can we delete secrets.GIT_HUB_PACKAGE_WRITE_TOKEN?
    # To avoid the error "unauthenticated: User cannot be authenticated with the token provided." while doing `npm update`
    - name: Create .npmrc for `npm update` or `npm i` to allow accessing our private repos in GPR
      run: |
        cd ./${{ inputs.repo-directory }}
        # To solve "code E403 npm ERR! 403 403 Forbidden - PUT https://npm.pkg.github.com/circles-zone/@circles-zone%2fdatabase-typeorm-local - Permission permission_denied: The token provided does not match expected scopes.
        echo "@circles-zone:registry=https://npm.pkg.github.com" > .npmrc
        # secrets.GITHUB_TOKEN didn't work error "Permission permission_denied: read_package", so I changed it to GH_READ_REPO_TOKEN
        # echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_CLASSIC_READ_PACKAGES_TOKEN }}" >> .npmrc
        echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_CLASSIC_READ_PACKAGES_TOKEN }}" >> .npmrc

    # As we want the team to be able to test it locally without GHA, we included the read:package token in .npmrc file so the developers install our private packages from GitHub Private Repository (GPR)
    # We need this only if the .npmrc does not exist
    # If we get 403 Permission permission_denied: read_package, we need to add read permission in the repo we try to access in https://github.com/orgs/circles-zone/packages/npm/database-without-orm-local-typescript-package/settings for example
    # Should we do npm install before npm ci?
    # 'npm ci' executing also 'npm tsc' which is equivalent to 'npm run prepare'
    # No need to create .npmrc before npm ci
    # In some cases it is needed to run both "npm i" and "npm ci" (i.e. marketplace-goods-graphql-typescript-serverless-com)
    # In the development environment it is better to run `npm i` and not `npm ci`
    # We can't create the .npmrc file before `npm i` - in `npm i` we use the .npmrc file in the repo with read token
    - name: npm update
      run: |
        cd ./${{ inputs.repo-directory }}
        #npm ci --ignore-scripts
        #npm i
        # In play1 we prefer `npm update`, in dvlp1 `npm i`
        echo "cat package.json"
        cat package.json
        echo "npm update"
        npm update

    # Some of the errors will be shown only if doing npx tsc (in case `npm i` is not executing npx tsc)
    - name: npx tsc
      run: |
        echo repo-directory=${{ inputs.repo-directory }}
        # Needed for repos such as https://github.com/circles-zone/queue-remote-graphql-local-typescript-package
        # TODO As we added the npm --prefix, do we need this cd?
        #cd ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}
        # Since repos such as phone-local-typescript didn't worked we removed the package-directory
        cd ./${{ inputs.repo-directory }}
        #tsc
        # TODO Is it only because the developer didn't install it? should it be after cd?
        #echo "npm install typescript- TODO Can we remote it?"
        # I think if we don't run `npm install typescript` we get error `This is not the tsc command you are looking for`
        npm install typescript
        # TODO Since we added the npm --prefix do we need those two commands?
        #pwd
        #ls -lag
        echo "npm run prebuild to create src/version.ts"
        # TODO How to avoid adding package-directory in the package.json of all repos
        npm run prebuild
        echo "npx tsc"
        npx tsc
      #env:
      # Reached heap limit Allocation failed - JavaScript heap out of memory
      # https://www.makeuseof.com/javascript-heap-out-of-memory-error-fix/
      # NODE_OPTIONS: --max-old-space-size=4096

    # Must have 1. scripts lint in package.json 2. .eslintrc.cjs
    - name: npm run lint
      run: |
        #cd ./${{ inputs.repo-directory }}
        cd ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}
        # TODO comment those two lines, uncommented as `npm i` converts the package.json to one line
        npm install eslint@latest --force --save-dev
        # Uncomment only in case the developer didn't install eslint-plugin
        npm install @typescript-eslint/eslint-plugin@latest --force --save-dev          
        npm run lint

    # If this repo accesses the database
    - name: Get GitHub Action (GHA) runner IP (In case of local and not remote package)
      if: inputs.is-rds-security-group == true
      id: ip
      uses: haythem/public-ip@master # v1.3.0 https://github.com/haythem/public-ip
      with:
        maxRetries: 50

    - name: Setting AWS_DEFAULT_REGION and AWS_SG_NAME environment variables (in case of local and not remote package)
      if: inputs.is-rds-security-group == true
      run: |
        echo "AWS_DEFAULT_REGION=us-east-1" >> $GITHUB_ENV
        # RDS/MySQL EC2 Security Group in Management/Master AWS Account 
        echo "AWS_SG_NAME=mysql_mang_sg" >> $GITHUB_ENV

    - name: Add GitHub Actions (GHA) runner IP to EC2 Security Group in Master/Management AWS Account (in case of local and not remote package)
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 authorize-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # Since RDS/MySQL is currently in Management/Master AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}

    # TODO We have this step twice both publish_typescript_package and publish_python_package - Can we use one logic for both 
    # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript?
    # TODO Should be copy/updated on regular basis also in run_typescript_package.yml
    - name: Set environment variables
      run: |
        # TODO update the repo-name
        #if ${{ inputs.is-run-local == true && inputs.repo-name == '...' }}; then
        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "is-run-local==true so setup RDS environment"
          echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          #echo "RDS_USERNAME=${{ vars.RDS_USERNAME }}" >> $GITHUB_ENV
          #echo "RDS_PASSWORD=${{ secrets.RDS_PASSWORD }}" >> $GITHUB_ENV
          echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
          echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not RDS repo"
        fi

        if ${{ inputs.repo-name == 'storage-main-local-typescript-package' ||
               inputs.repo-name == 'contact-user-external-local-python-package' }}
        then
          echo "Setup Circlez.ai AWS Storage Environment Variables"
          echo "AWS_DEFAULT_STORAGE_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV  

          # Alias
          echo "STORAGE_AWS_DEFAULT_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Circlez.ai AWS Storage repo"
        fi

        # I'm trying to remove those from authentication-remote-typescript
        # TODO update the repo-name
        if ${{ inputs.is-run-local == true && inputs.repo-name == 'storage-main-local-typescript-package' }}
        then
          echo "Setup Circlez.ai AWS Access Environment Variables"
          echo "AWS_ACCESS_KEY_ID=${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV         
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV         
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
        else
          echo "Not a Circlez.ai AWS Access repo"
        fi

        # I'm trying to remove those from authentication-remote-typescript
        # TODO update the repo-name
        if ${{ inputs.is-run-local == true && inputs.repo-name == '...' }}
        then
          echo "Setup Circlez.ai AWS Cognito (User Pool) Environment Variables"
          echo "USER_POOL_ID=${{ vars[format('USER_POOL_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV         
          echo "USER_POOL_WEB_CLIENT_ID=${{ vars[format('USER_POOL_WEB_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Circlez.ai AWS Cognito (User Pool) repo"
        fi

        if ${{ inputs.repo-name == 'generic-remote-graphql-typescript-package' }}
        then
          echo "Setup Circlez.ai AWS TEST_GRAPHQL_APPSYNC_API_KEY Environment Variables"
          echo "TEST_GRAPHQL_APPSYNC_API_KEY=${{ secrets.TEST_GRAPHQL_APP_SYNC_API_KEY }}" >> $GITHUB_ENV
        else
          echo "Not a Circlez.ai AWS TEST_GRAPHQL_APPSYNC_API_KEY repo"
        fi

        if ${{ inputs.repo-name == 'location-remote-graphql-local-typescript-package' }}
        then
          echo "Setup Circlez.ai AWS LOCATION_GRAPHQL_APPSYNC_API_KEY Environment Variables"
          #echo "LOCATION_GRAPHQL_APPSYNC_API_KEY=${{ secrets.LOCATION_GRAPHQL_APPSYNC_API_KEY }}" >> $GITHUB_ENV
          echo "LOCATION_GRAPHQL_APPSYNC_API_KEY=${{ secrets[format('LOCATION_GRAPHQL_APPSYNC_API_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          else
          echo "Not a Circlez.ai AWS LOCATION_GRAPHQL_APPSYNC_API_KEY repo"
        fi

        if ${{ inputs.repo-name == 'message-remote-graphql-typescript-package' }}
        then
          echo "Setup Circlez.ai AWS MESSAGE_GRAPHQL_APPSYNC_API_KEY Environment Variables"
          #echo "MESSAGE_GRAPHQL_APPSYNC_API_KEY=${{ secrets.MESSAGE_GRAPHQL_APPSYNC_API_KEY }}" >> $GITHUB_ENV
          echo "MESSAGE_GRAPHQL_APPSYNC_API_KEY=${{ secrets[format('MESSAGE_GRAPHQL_APPSYNC_API_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV

        else
          echo "Not a Circlez.ai AWS MESSAGE_GRAPHQL_APPSYNC_API_KEY repo"
        fi

        # TODO Why this is -local-, it should be remote, right?
        #if ${{ inputs.is-run-local == true && inputs.repo-name == 'queue-remote-graphql-local-typescript-package' }}
        if ${{ inputs.repo-name == 'queue-remote-graphql-local-typescript-package' }}; then
          echo "Setup Circlez.ai AWS QUEUE_GRAPHQL_APPSYNC_API_KEY Environment Variables"
          # echo "QUEUE_GRAPHQL_APPSYNC_API_KEY=${{ secrets.QUEUE_GRAPHQL_APPSYNC_API_KEY }}" >> $GITHUB_ENV
          echo "QUEUE_GRAPHQL_APPSYNC_API_KEY=${{ secrets[format('QUEUE_GRAPHQL_APPSYNC_API_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Circlez.ai AWS QUEUE_GRAPHQL_APPSYNC_API_KEY repo"
        fi

        #EVENT_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.EVENT_GRAPHQL_APPSYNC_API_KEY }}
        #QUEUE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.QUEUE_GRAPHQL_APPSYNC_API_KEY }}
        #TEST_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.TEST_GRAPHQL_APP_SYNC_API_KEY }} # Implemented above
        #MESSAGE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.MESSAGE_GRAPHQL_APPSYNC_API_KEY }}


    # Should we use "npm test --runInBand"?
    - name: npm test (jest) - It was Local but now it will be both Local and Remote using jest.config.js
      #if : ${{ inputs.is-run-local }}
      run: |
        #cd ./${{ inputs.repo-directory }}
        cd ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}
        # https://stackoverflow.com/questions/55991641/npm-test-coverage-never-exits
        # I added '-- coverage'
        #echo "pwd"
        #pwd
        #echo "ls -lag"
        #ls -lag
        #echo "ls tests -lag"
        #ls tests -lag
        npm test --detectOpenHandles --coverage
      env:
        # TODO: Please comment and then delete all variables not being used.
        # TODO: In case remote-typescript-package we need only ENVIRONMENT:
        # TODO Check if we want to copy from storage-local
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # Only is-run-local should have access to the database
        # TODO: RDS environment variables are needed for Local which accesses the RDS (MySQL) and should be deleted in case of Remote
        # Commened as we moved it to the previous step to make it conditional
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        DEBUG: ${{ inputs.is-debug-mode }} # OLD, should be replaced by "LOGGER_MINIMUM_SEVERITY: Warning" and .logger.json
        # https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        #LOGGER_MINIMUM_SEVERITY: 1
        # Logger TypeScript is still not using LOGGER_MINIMAL_SEVIRITY as the Python logger does
        #LOGGER_MINIMUM_SEVERITY: Warning
        #logger-is-write-to-sql

        # Used by storage-local ...
        # 2delete renamed to AWS_DEFAULT_REGION (so we'll have prefix to all variable)
        #DEFAULT_AWS_REGION: ${{ vars.AWS_REGION }}

        # Commented as we gradually move it to the previous step and make it conditional
        #LOCATION_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.LOCATION_GRAPHQL_APPSYNC_API_KEY }}
        #EVENT_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.EVENT_GRAPHQL_APPSYNC_API_KEY }}
        #QUEUE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.QUEUE_GRAPHQL_APPSYNC_API_KEY }}
        #TEST_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.TEST_GRAPHQL_APP_SYNC_API_KEY }}
        #MESSAGE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.MESSAGE_GRAPHQL_APPSYNC_API_KEY }}
        
        # For Storage
        # TODO I didn't manage to move this to the previous step as shell script
        #AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}

        # Resolve "Failed to authenticate"
        #TODO Can we comment the below line?
        #USER_JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }} # If we need to access our APIs, resolve the error "Failed to authenticate"

        # Needed by https://github.com/circles-zone/user-main-local-typescript-package
        # Line bellow commented as moved to the previous step if statement, so we will have it only when we really need it
        # TODO Authentication-remote new works with this, authenticaiton-remote old don't have it and doesn't work - I want to remote it from is-run-remote only in is-run-local
        #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
        #USER_POOL_ID: ${{secrets[format('USER_POOL_ID_{0}', inputs.environment-name)]}}
        #USER_POOL_WEB_CLIENT_ID: ${{ secrets[format('USER_POOL_WEB_CLIENT_ID_{0}', inputs.environment-name)] }}

    # Should we use "npm test --runInBand"?
    - name: npm test (jest?) Remote - I think we can delete this step
      #if : ${ inputs.is-run-remote }
      if: false
      run: |
        #cd ./${{ inputs.repo-directory }}
        cd ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}
        # https://stackoverflow.com/questions/55991641/npm-test-coverage-never-exits
        # I added '-- coverage'
        echo "Method 1"
        npm test --detectOpenHandles --coverage
        echo "Method 2"
        npx jest --coverage --coverageReporters json-summary
        echo "Method 3"
        npm test --coverage --coverageReporters json-summary
      env:
        # TODO: Please comment and then delete all variables not being used.
        # TODO: In case remote-typescript-package we need only ENVIRONMENT:
        # TODO Check if we want to copy from storage-local
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # Only is-run-local should have access to the database
        # TODO: RDS environment variables are needed for Local which accesses the RDS (MySQL) and should be deleted in case of Remote
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        # TODO can we access vars?
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

        # TODO We better not use Sanity in remote to make sure it works without access to the database
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        # TODO can we access vars?
        #RDS_USERNAME: ${{ vars[format('SANITY_RDS_USERNAME_{0}', inputs.environment-name)] }}
        #RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        DEBUG: ${{ inputs.is-debug-mode }} # OLD, should be replaced by "LOGGER_MINIMUM_SEVERITY: Warning" and .logger.json
        # https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        #LOGGER_MINIMUM_SEVERITY: 1
        # Logger TypeScript is still not using LOGGER_MINIMAL_SEVIRITY as the Python logger does
        #LOGGER_MINIMUM_SEVERITY: Warning
        #logger-is-write-to-sql

        # Used by storage-local ...
        # Renamed to AWS_DEFAULT_REGION so we will have prefix to all variables
        #DEFAULT_AWS_REGION: ${{ vars.AWS_REGION }}

        LOCATION_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.LOCATION_GRAPHQL_APPSYNC_API_KEY }}
        EVENT_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.EVENT_GRAPHQL_APPSYNC_API_KEY }}
        QUEUE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.QUEUE_GRAPHQL_APPSYNC_API_KEY }}
        TEST_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.TEST_GRAPHQL_APP_SYNC_API_KEY }}
        MESSAGE_GRAPHQL_APPSYNC_API_KEY: ${{ secrets.MESSAGE_GRAPHQL_APPSYNC_API_KEY }}
        
        # For Storage
        #AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}

        # Resolve "Failed to authenticate"
        #TODO Can we comment the below line?
        #USER_JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }} # If we need to access our APIs, resolve the error "Failed to authenticate"

        # Needed by https://github.com/circles-zone/user-main-local-typescript-package
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
        USER_POOL_ID: ${{secrets[format('USER_POOL_ID_{0}', inputs.environment-name)]}}
        USER_POOL_WEB_CLIENT_ID: ${{ secrets[format('USER_POOL_WEB_CLIENT_ID_{0}', inputs.environment-name)] }}


    - name: Remove Github Actions IP from AWS Security Group (SG)  (in case of local and not remote package)
      #if: always()
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 revoke-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # Since RDS/MySQL in Master/Management AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}

    # https://github.com/MishaKav/jest-coverage-comment
    # Documentation https://github.com/MishaKav/jest-coverage-comment/tree/main/
    # After npx jest
    - name: Jest Coverage Comment
      uses: MishaKav/jest-coverage-comment@main # 1.0.24

  # Commenting on this job
  # Pro: no duplicate steps
  # Cons: we can't re-run this job
  #publish-gpr:
    #needs: build_publish
    #runs-on: ubuntu-latest
    # It seems we don't need this
    # needed for react.js for "npm build"
    #permissions:
      #contents: read
      #contents: write # Needed for phips28/gh-action-bump-version
      #packages: write # Must have write permission to create/update the package
    #strategy:
      #matrix:
        #node-version: [20.x]      
    #steps:
    #- uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout

    #- uses: actions/setup-node@main # v4.0.1 # https://github.com/actions/setup-node
      #with:
        #node-version: 20 # 16 -> 20
        #node-version: ${{ matrix.node-version }}
        # It seems we don't need those
        ##registry-url: https://npm.pkg.github.com/
        ## scope: "@circles-zone"

    # In case the .npmrc from our template was changed by phips28/gh-action-bump-version in previous execution
    # Create .npmrc in case it doesn't exist or the phips28/gh-action-bump-version replaced the authToken
    # Needed for `npm update` or `npm i`
    # To avoid error "unauthenticated: User cannot be authenticated with the token provided." while doing `npm update`
    #- name: Create .npmrc for `npm update` or `npm i` to allow accessing our private repos in GPR
      #run: |
        # TODO fix the directory structure and uncomment
        #cd ./${{ inputs.repo-directory }}
        # To solve "code E403 npm ERR! 403 403 Forbidden - PUT https://npm.pkg.github.com/circles-zone/@circles-zone%2fdatabase-typeorm-local - Permission permission_denied: The token provided does not match expected scopes.
        #echo "@circles-zone:registry=https://npm.pkg.github.com" > .npmrc
        # secrets.GITHUB_TOKEN didn't work error "Permission permission_denied: read_package", so I changed it to GH_READ_REPO_TOKEN
        #echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_READ_REPO_TOKEN }}" >> .npmrc      
    
    # `npm i` works better for us than npm ci (i.e. local-storage-typescript backend) 
    # if we get 403 Permission permission_denied: read_package, we need to add read permission in the repo we try to access in https://github.com/orgs/circles-zone/packages/npm/database-without-orm-local-typescript-package/settings for example    
    #- name: npm i
      #run: |
        #cd ./${{ inputs.repo-directory }}
        #npm i

    # TODO We don't need it and this can be deleted
    # Increase the version number instead of updating package.json manually (other solution might be 'grunt bump:patch')
    #- run: git config --global user.email "<EMAIL>"
    #- run: git config --global user.name "Circles"

    - name: Extract branch name
      if: inputs.is-bump-version != false
      shell: bash
      run: |
        echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}"
      id: extract_branch

    # It was after bump, I moved it before bump to try to resolve permission problem - Didn't solve. TODO We can move this section after the bump.
    # This is mandatory in TypeScript-Package to publish the package 
    # We must provide secrets.GITHUB_TOKEN with "packages: write" permissions to create/update the package
    # We can't create the .npmrc file before `npm i` - in `npm i` we use the .npmrc file in the repo with read token
    # Authenticate PUT with a private GitHub package to avoid 401
    - name: Create .npmrc for publishing to GPR
      run: |
        # To solve "code E403 npm ERR! 403 403 Forbidden - PUT https://npm.pkg.github.com/circles-zone/@circles-zone%2fdatabase-typeorm-local - Permission permission_denied: The token provided does not match expected scopes.

        #echo "@circles-zone:registry=https://npm.pkg.github.com" > ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}/.npmrc
        # To support also platform-metrics which is in the new directory stucture
        echo "@circles-zone:registry=https://npm.pkg.github.com" > ./${{ inputs.repo-directory }}/.npmrc

        #echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" >> ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}/.npmrc
        # I think we can't use fine grade token, only classical token
        #echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_CLASSIC_WRITE_PACKAGES_TOKEN }}" >> ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}/.npmrc
        # To support also platform-metrics which is in the new directory stucture
        echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_CLASSIC_WRITE_PACKAGES_TOKEN }}" >> ./${{ inputs.repo-directory }}/.npmrc
        #echo "//npm.pkg.github.com/:_authToken=${{ secrets.GH_FINE_GRAINED_WRITE_REPO_CONTENTS_TOKEN }}" >> ./${{ inputs.repo-directory }}/.npmrc

    # Alternatives:
    # npm version patch
    # https://github.com/florian-h05/gh-action-advanced-bump-version/blob/main/.github/workflows/autopublish.yml
    # From phips28/gh-action-bump-version

    # Error: "remote: Write access to repository not granted." "fatal: unable to access"
    #  "permissions: contents: write" # Needed for phips28/gh-action-bump-version and phips28/gh-action-bump-version@master

    # "fatal     remote: Write access to repository not granted." Solution: "permissions contents: write" Actions-General-Actions Permissions
    # "error: failed to push some refs to", the solution probably "secrets.GH_CLASSIC_READ_PACKAGES_TOKEN"
    # "error: failed to push some refs to" "hint: Updates were rejected because the tag already exists in the remote." - Update package.json with the current installed version
    - name: 'Automated Version Bump in TypeScript Package Template XXX'
      #if: inputs.is-bump-version != false
      if: inputs.is-bump-version == true 
      uses:  'phips28/gh-action-bump-version@master' # https://github.com/phips28/gh-action-bump-version
      env:
        # error: "fatal remote: Write access to repository not granted."
        #GITHUB_TOKEN: ${{ secrets.GH_CLASSIC_READ_PACKAGES_TOKEN }}
        # Doesn't allow to update the packge after bump
        #GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        #GITHUB_TOKEN: ${{ inputs.repo-token }}
        # Chaged to allow to write to the repo in this step. - Didn't work
        GITHUB_TOKEN: ${{ secrets.GH_CLASSIC_WRITE_PACKAGES_TOKEN }}
        # Maybe doesn't work because of .npmrc
        PACKAGEJSON_DIR: ${{ inputs.repo-directory }}
      with:
        minor-wording:  'add,Adds,new'
        major-wording:  'MAJOR,cut-major'
        patch-wording:  'patch,fixes'     # Providing patch-wording will override commits
                                          # defaulting to a patch bump.
        rc-wording:     'RELEASE,alpha'
        tag-prefix:     'v'
        tag-suffix:     '-${{ inputs.environment-name }}-${{ steps.extract_branch.outputs.branch }}'
        skip-commit:    'true' # Due to a warning because we use checkout newer than v1, Do we have tags?

    # Should make sure the package.json version is unique
    # Should add @circles-zone/ to the package "name" in package.json
    # If there is no "name" in package.json you will get ENEEDAUTH error
    # If we have the error "code E403" "Permission permission_denied: The token provided does not match expected scopes." error we need to generate ./${{ inputs.repo-directory }}/.npmrc in previous step
    # If we have error "Cannot publish over existing version" error, we should update the version in package.json
    # If we have the error "npm ERR! code ENEEDAUTH, npm ERR! need auth This command requires you to be logged in to https://registry.npmjs.org/, npm ERR! need auth You need to authorize this machine using `npm adduser`" We might running publish_typescript instead of deploy_typescript
    - name: npm publish to GPR (using .npmrc)
      run: |
        cd ./${{ inputs.repo-directory }}
        #Huskey ".git can't be found" error
        #cd ./${{ inputs.repo-directory }}/${{ inputs.package-directory }}

        # Running those two commands in case we added Huskey scripts to package.json and did not install it locally (can be removed in the future)
        #npm install npm-run-all --save-dev
        #npm i husky -D

        echo "pwd"
        pwd
        echo "ls -lag"
        ls -lag
        npm publish
      env:
        NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
