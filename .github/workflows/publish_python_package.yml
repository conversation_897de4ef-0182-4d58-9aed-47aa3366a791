# TODO: Replace <project-name> everywhere with the full name of the repo (Some places with a hyphen and some places with an underscore)
# https://towardsdatascience.com/setting-up-python-projects-part-iii-56aafde8ae0b

# V3 if: startsWith(github.ref, 'refs/heads/bu-')

# GitHub Template using this reusable workflow
#   https://github.com/circles-zone/python-package-template/blob/dev/.github/workflows/build_publish_project_name_python_package_play1.yml

# Local Package used in / example:
#   V3 https://github.com/circles-zone/database-mysql-local-python-package/blob/dev/.github/workflows/build-publish-database-mysql-local-python-package-play1.yml
#   V1 https://github.com/circles-zone/people-local-python-package/blob/BU-2204-create-people-local-to-process-contacts-information--<PERSON><PERSON>-<PERSON>ali<PERSON>di-/.github/workflows/build_publish_people_local_python_package_play1.yml
#   V2 https://github.dev/circles-zone/contact-group-local-python-package/tree/BU-2232--change-the-method-add_update_group_and_link_to_contact--Tal-Goodman-/.github
#   V2 https://github.com/circles-zone/visibility-local-python-package/blob/BU-2252-add-enums-to-visibility-local/.github/workflows/build_publish_visibility_local_python_package_play1.yml

# Remote Package used in / example: 
#   V1 https://github.com/circles-zone/smartlink-remote-restapi-python-package/blob/dev/.github/workflows/build_publish_project_name_python_package_play1.yml

# In the process in
# https://github.com/circles-zone/profile-reddit-restapi-imp-local-python-package

name: Build Publish Python Package to PyPI.org

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      # TODO We didn't manage to have multiple environments using
      #   target-environments: [ play1, dvlp1, prod1 ]
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: true
        type: string
      branch-name:
        required: false
        type: string
        default: ${{ github.ref }}
        description: "Mostly we use the default branch name"
      repo-directory:
        required: true
        type: string
        # TODO Didn't manage to do it
        #default: ${{ github.event.inputs.repo-name }}
        #default: ${{ inputs.repo-name.default }}
      package-directory:
        required: false
        type: string
        default: .
      # inputs.environment_name we don't need to add it to the testing env:
      # inputs.environment-name is the standard, we need to add it to the testing env:
      is-rds-security-group:
        required: false
        type: boolean
        default: false
      component-name:
        required: false
        type: string
      #is_run_local:
        #required: false
        #type: string    
      #is_run_remote:
        #required: false
        #type: string
      sql2code-command-line:
        required: false
        type: string        
      is-publish-to-test-pypi:
        required: false
        type: boolean
        # TODO How can the default value be dependent on the environment
        default: false
      is-publish-to-non-test-pypi:
        required: false
        type: boolean
        # TODO How can the default value be dependent on the environment
        default: true
      #co: # Better we leave it 'false'
        #required: false
        #type: string    
      #SQL_USERNAME:
        #required: false # required in local, not required in remote
        #type: string
      google-user:
        required: false
        type: string
        default: <EMAIL>
      logger-minimum-severity:
        required: false
        type: string
        default: Warning
      logger-is-write-to-sql:
        required: false
        type: boolean
        default: false
      is-run-local:
        required: false
        type: boolean
        default: true
      is_run_selenium:
        required: false
        type: boolean
        default: false
      opensearch_host:
        required: false
        type: string
      opensearch_initial_admin_password:
        required: false
        type: string

    #secrets:
      #RDS_PASSWORD:
        #required: true
      #OPENSEARCH_INITIAL_ADMIN_PASSWORD:
        #required: false
    # Map the workflow outputs to job outputs
    #outputs:
      #firstword:
        #description: "The first output string"
        #value: ${{ jobs.example_job.outputs.output1 }}
      #secondword:
        #description: "The second output string"
        #value: ${{ jobs.example_job.outputs.output2 }}

#permissions:
  #contents: read

# Mandatory for https://github.com/pypa/gh-action-pypi-publish
permissions:
  id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
  
jobs:
  build_publish_python_package:
    # ${{ inputs.repo-name }} works
    # Name of the script in the beginning
    name: Publish Python Package ${{ inputs.repo-name }} Build, Test, and Publish Python Package
    if: ${{ contains(github.event.head_commit.message, '[pub]') || contains(github.event.head_commit.message, '[run]') }}
    runs-on: ubuntu-latest
    # ProfileFBSeleScraImpP()Play1 needs more than 5 minutes
    # profile-zoominfo-graphql-imp-local-python-package needs more than 5 minutes
    # contact-person-profile-csv-imp-local-python-package need more than 10 minutes
    # database-mysql-python-local-python more than 15 minutes
    # TODO run python profile on packages with need more than 19 minutes
    timeout-minutes: 30
    environment: 
      name: ${{ inputs.environment-name }} # If using $environment-name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ inputs.environment-name }}.circ.zone
    strategy:
      # Trying to allow the play1 job to work although the dvlp1 job failed due to environment protection rules.
      #fail-fast: false
      matrix:
        #python-version: ["3.12"] # We changed from python-version 3.x (which was 3.12) to 3.10 so we can use torch in requirements.txt in a Python Package
        #python-version: ['3.x'] # Latest 3.x version (Currently 3.12)
        python-version: ['3.11'] # Maybe faster
        #poetry-version: ["2.1.6"]
    # "permission: id-token: write" cause actions/checkout to this error message "remote: Repository not found."
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # TODO I added it to try allowing Sql2Code to update the repo. We might want to move the Sql2Code to a separate job (not step) and give permission to that job
      pull-requests: write # Allow Sql2Code to create Pull Request with the changes
    #permissions: write-all
    steps:
    - name: Checkout the code of this repo
      uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
          # Repository name with owner. For example, actions/checkout
          # Default: ${{ github.repository }}
          repository: circles-zone/${{ inputs.repo-name }}
          token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
          ref: ${{ inputs.branch-name }}
          # Needed for:
          #   database-mysql-local-python-package (sql2code)
          submodules: true

    # TODO Shall we do it in the beginning and update the version in the code? or after all tests passed (i.e. test, lint ...)? - I prefer as soon as possible
    # TODO Shall we do it before we create a pull request? Should it be a separate pull request?
    - name: Patch the version number in pyproject.toml
      run: |
        pip install poetry
        #echo "pwd"
        #ls -lag
        # Without cd doesn't work in variable-local-python-package
        echo "ls -lag"
        ls -lag
        echo "cd ./${{ inputs.repo-directory }}"
        cd ./${{ inputs.repo-directory }}
        echo "ls -lag pyproject.toml"
        ls -lag pyproject.toml
        poetry version patch

    # Needed both for sql2code and lint/linter steps
    - name: git submodule update
      run: |
        # TODO Which one shall we run?
        echo "--- git submodule update --init --recursive"
        git submodule update --init --recursive
        echo "--- git submodule update --init --recursive --remote"
        git submodule update --init --recursive --remote

    # Must be after checkout
    # Moved from after "checkout" to after "Install dependencies"
    # TODO It is not good idea to run sql2code which uses sql-database-local before we build sql-database-local (when mysql-database-local is not working) 
    # Used in:
    #   database-mysql-local-python-package
    #     GHA YML https://github.com/circles-zone/database-mysql-local-python-package/blob/dev/.github/workflows/build-publish-database-mysql-local-python-package-play1.yml
    #   logger-local-python-package

    # https://github.com/marketplace/actions/create-pull-request
    # TODO Shall we use PAT or GITHUB_TOKEN?
    # TODO update parameters in 'with'
    - name: Create Pull Request (for sql2code and other changes in the code)
      #uses: peter-evans/create-pull-request@v6
      #uses: peter-evans/create-pull-request@v7.0.8
      # Due to error message in https://github.com/circles-zone/user-main-local-python-package/ "are not allowed to be used in circles-zone/user-main-local-python-package. Actions in this workflow must be: within a repository owned by circles-zone, created by GitHub, or verified in the GitHub Marketplace."
      uses: circles-zone/create-pull-request@main

    - name: Replace _view to _table in test*.py due to allow access to deleted data
      run: |
        # TODO only test*.py in tests directory
        # TODO The sed should work only between " " in SQL Statement (and not parameters) and not in other places in the code i.e. comments/TODOs
        #find . \( -type d -name src -prune \) -o -type f -print0 | xargs -0 sed -i 's/_view/_table/g'

    # https://github.com/marketplace/actions/create-pull-request
    # TODO Shall we use PAT or GITHUB_TOKEN?
    # TODO update parameters in 'with'
    - name: Create Pull Request (for sql2code and other changes in the code)
      #TODO Temp
      if: false
      #uses: peter-evans/create-pull-request@v6
      #uses: peter-evans/create-pull-request@v7.0.8
      # Due to error message in https://github.com/circles-zone/user-main-local-python-package/ "are not allowed to be used in circles-zone/user-main-local-python-package. Actions in this workflow must be: within a repository owned by circles-zone, created by GitHub, or verified in the GitHub Marketplace."
      uses: circles-zone/create-pull-request@main

  # TODO stop the job if in test*.py in tests directory there is _table
  # TODO stop if there is no tests directory

    - name: Setup Python
      uses: actions/setup-python@main # v5.1.0 # https://github.com/actions/setup-python
      with:
        #python-version: '3.x'
        python-version: ${{ matrix.python-version }}

    # Do we need it? For what? - It is not mandatory
    - name: Cache pip
      uses: actions/cache@main # v3.3.2 # https://github.com/actions/cache
      with:
        # This path is specific to the Ubuntu
        path: ~/.cache/pip
        # Look to see if there is a cache hit for the corresponding requirements file
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          ${{ runner.os }}-


    # This step uses setup.py
    - name: Install dependencies
      run: |
        #echo --- root
        #ls -lag
        #echo --- repo directory ./${{ inputs.repo-directory }}
        #ls -lag ./${{ inputs.repo-directory }}
        #echo --- package directory ./${{ inputs.repo-directory }}/${{ inputs.package_name }}
        #ls -lag ./${{ inputs.repo-directory }}/$package_name

        cd ./${{ inputs.repo-directory }}
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        #if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        # It allows us to omit the "try/except" in the imports
        # We comment this because of "package directory does not exist"
        #pip install .


    - name: Lint with flake8
      run: |
        python -m pip install flake8 pytest
        # Make sure those are sync with make_py.ps1 (to avoid extra costs FinOps)
        # Stops the build if there are Python syntax errors or undefined names
        echo "flake8 #1 --select=E9,F63,F7,F82"
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        echo "flake8 --exit-zero --max-complexity=10"
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics        


    # TODO Make sure the inner directory name is identical to the PACKAGE_NAME in setup.py with underline
    # Mandatory for creating /dist and publishing, in the repo-name directory
    - name: Build package
      run: |
        cd ./${{ inputs.repo-directory }}
        pip install build
        python -m build

    # TODO Do we need it?
    # Increase the version number instead of updating package.json manually (other solution might be 'grunt bump:patch')
    - run: git config --global user.email "<EMAIL>"
    - run: git config --global user.name "Circles"
    #- run: npm version patch

    # If this repo accesses the database
    - name: Get GitHub Action (GHA) runner IP
      if: inputs.is-rds-security-group == true
      id: ip
      #uses: haythem/public-ip@master # v1.3.0 https://github.com/haythem/public-ip
      # Due to error message in https://github.com/circles-zone/user-main-local-python-package/ "are not allowed to be used in circles-zone/user-main-local-python-package. Actions in this workflow must be: within a repository owned by circles-zone, created by GitHub, or verified in the GitHub Marketplace."
      uses: circles-zone/public-ip@master # v1.3.0 https://github.com/haythem/public-ip
      with:
        maxRetries: 50

    - name: Setting AWS_DEFAULT_REGION and AWS_SG_NAME environment variables.
      if: inputs.is-rds-security-group == true
      run: |
        echo "AWS_DEFAULT_REGION=us-east-1" >> $GITHUB_ENV
        # RDS/MySQL EC2 Security Group in Management/Master AWS Account 
        echo "AWS_SG_NAME=mysql_mang_sg" >> $GITHUB_ENV

    - name: Add GitHub Actions (GHA) runner IP to EC2 Security Group in Master/Management AWS Account
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 authorize-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # Since RDS/MySQL is currently in Management/Master AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}


    # TODO We have this step twice both publish_typescript_package and publish_python_package - Can we use one logic for both 
    # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript? https://docs.github.com/en/actions/creating-actions/creating-a-composite-action
    # TODO How can we avoid maintaining this step in multiple places (i.e. publish_python_package, publish_typescript_package, run_python, run_typescript ...)
    - name: Set environment variables
      run: |
        echo "inputs.repo-name=${{ inputs.repo-name }}"

        #echo "SLACK_USERNAME=Github Actions" >> $GITHUB_ENV
        #echo "SLACK_ICON_EMOJI=:ocotcat:" >> $GITHUB_ENV
        #echo "SLACK_TITLE=This is a GitHub Actions build!" >> $GITHUB_ENV
        #echo "SLACK_WEBHOOK=${{ secrets.SLACK_WEBHOOK }}" >> $GITHUB_ENV
        #echo "SLACK_CHANNEL=#channel" >> $GITHUB_ENV
  
        #if [[ ${{ needs.build.result }} == success ]]; then
        if [[ ${{ !inputs.is-run-anonymous  }} ]]; then
          echo "PRODUCT_USER_IDENTIFIER=${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "PRODUCT_PASSWORD=${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        #else
        fi

        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "is-run-local==true so setup RDS environment"
          echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
          echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        else # inputs.is-run-remote
          echo "Not RDS repo"
        fi

        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "is-run-local==true so setup PostgreSQL environment"
          echo "POSTGRESQL_HOSTNAME=${{ vars[format('POSTGRESQL_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "POSTGRESQL_USERNAME=${{ vars[format('POSTGRESQL_USERNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "POSTGRESQL_PASSWORD=${{ secrets[format('POSTGRESQL_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        else # inputs.is-run-remote
          echo "Not PostgreSQL repo"
        fi

        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "is-run-local==true so setup REDIS environment"
          echo "REDIS_HOSTNAME=${{ vars[format('REDIS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "REDIS_PORT=${{ vars[format('REDIS_PORT_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          #echo "REDIS_USERNAME=${{ vars.REDIS_USERNAME }}" >> $GITHUB_ENV
          echo "REDIS_PASSWORD=${{ secrets[format('REDIS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        else # inputs.is-run-remote
          echo "Not REDIS repo"
        fi

        # TODO I'm not sure what is REDDIT_USERNAME - I'm guessing
        if ${{ inputs.repo-name == 'profile-reddit-restapi-imp-local-python-package' }}
        then
          echo "Setup REDDIT Environment Variables"
          echo "REDDIT_USERNAME=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
          # TODO Should be included only in circles-zone/profile-reddit-restapi-imp-local-python-package
          echo "REDDIT_CLIENT_ID=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
          echo "REDDIT_CLIENT_SECRET=${{ secrets.REDDIT_CLIENT_SECRET }}" >> $GITHUB_ENV
        else
          echo "Not REDDIT repo"
        fi

        if ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }}
        then
          echo "Setup ZOOMINFO Environment Variables"
          echo "ZOOMINFO_APPLICATION_CLIENT_ID=${{ vars.ZOOMINFO_APPLICATION_CLIENT_ID }}" >> $GITHUB_ENV
          echo "ZOOMINFO_APPLICATION_ACCOUNT_ID=${{ vars.ZOOMINFO_APPLICATION_ACCOUNT_ID }}" >> $GITHUB_ENV
          echo "ZOOMINFO_APPLICATION_CLIENT_SECRET=${{ secrets.ZOOMINFO_APPLICATION_CLIENT_SECRET }}" >> $GITHUB_ENV

          echo "TEST_ZOOMINFO_USER_ID=${{ vars.TEST_ZOOMINFO_USER_ID }}" >> $GITHUB_ENV
          echo "TEST_ZOOMINFO_FIRST_NAME=${{ vars.TEST_ZOOMINFO_FIRST_NAME }}" >> $GITHUB_ENV
          echo "TEST_ZOOMINFO_LAST_NAME=${{ vars.TEST_ZOOMINFO_LAST_NAME }}" >> $GITHUB_ENV
          echo "TEST_ZOOMINFO_EMAIL=${{ vars.TEST_ZOOMINFO_EMAIL }}" >> $GITHUB_ENV
        else
          echo "Not ZOOMINFO repo"
        fi

        if ${{ inputs.repo-name == 'location-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'real-estate-realtor-com-selenium-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-location-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-contact-local-python-package'}} ; then
          echo "Setup Opencage Environment Variables"
          echo "OPENCAGE_KEY=${{ secrets.OPENCAGE_KEY }}" >> $GITHUB_ENV
        else
          echo "Not Opencage repo"
        fi

        if ${{ inputs.repo-name == 'message-main-local-python-package' }} \
         || ${{ inputs.repo-name == 'whatsapp-message-inforu-local-python-package-serverless-com' }} \
         || ${{ inputs.repo-name == 'message-main-local-python-package' }} ; then
          echo "Setup Messages Environment Variables"
          # TODO SENDER -> FROM
          echo "MESSAGE_TEST_SENDER_PROFILE_ID=${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "MESSAGE_TEST_TO_PHONE_NUMBER=${{ vars[format('MESSAGE_TEST_TO_PHONE_NUMBER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Messages repo"
        fi

        if ${{ inputs.repo-name == 'business-profile-yelp-graphql-imp-local-python-package' }} ; then
          echo "Setup Yelp Environment Variables"
          echo "YELP_API_KEY=${{ vars.YELP_API_KEY }}" >> $GITHUB_ENV
        else
          echo "Not a Yelp repo"
        fi

        # TODO we have GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in user-external-main-local-python-package
        if ${{ inputs.repo-name == 'user-external-local-python-package' }}; then
          echo "Setup User External GCP/Google Project Environment Variables"
          # echo "GOOGLE_SHEETS_CLIENT_ID=${{ vars[format('GOOGLE_SHEETS_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # echo "GOOGLE_SHEETS_CLIENT_SECRET=${{ secrets[format('GOOGLE_SHEETS_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Google Sheets repo"
        fi

        #if ${{ inputs.repo-name == 'google-account-local-python-package' }}; then
          echo "Setup Google Account/Authentication Environment Variables"
          # echo "GOOGLE_ACCOUNT_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # echo "GOOGLE_ACCOUNT_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        #else
          #echo "Not a Google Account/Authentication repo"
        #fi

        if ${{ inputs.repo-name == 'google-contact-local-python-package' }}; then
          echo "Setup Google Contact Environment Variables"
          # echo "GOOGLE_CONTACTS_CLIENT_ID=${{ vars[format('GOOGLE_CONTACTS_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # echo "GOOGLE_CONTACTS_CLIENT_SECRET=${{ secrets[format('GOOGLE_CONTACTS_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # for 
          echo "GOOGLE_ACCOUNT_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_ACCOUNT_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # google-contact-local-python-package
          echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Google Contacts repo"
        fi

        if ${{ inputs.repo-name == 'google-sheets-local-python-package' }}; then
          echo "Setup Google Sheets Environment Variables"
          # echo "GOOGLE_SHEETS_CLIENT_ID=${{ vars[format('GOOGLE_SHEETS_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # echo "GOOGLE_SHEETS_CLIENT_SECRET=${{ secrets[format('GOOGLE_SHEETS_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_ACCOUNT_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_ACCOUNT_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV

        else
          echo "Not a Google Sheets repo"
        fi

        # TODO Delete the bellow
        if ${{ inputs.repo-name == 'google-contact-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-account-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-sheets-local-python-package' }} ; then
          echo "Setup Google Authentication Environment Variables"
          # NOT GOOD
          # echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # TODO Do we need this
          # echo "GOOGLE_PROJECT_ID=${{ vars[format('GOOGLE_PROJECT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_PORT_FOR_AUTHENTICATION=${{ vars.GOOGLE_PORT_FOR_AUTHENTICATION }}" >> $GITHUB_ENV
          #echo "GOOGLE_REDIRECT_URIS=http://localhost:54219/" >> $GITHUB_ENV
          # TODO Will it work in GHA? No, Interactive Google Authentication do not work in GHA. Do we need it here? If so, can we make it GHA Variable and not hard coded? We have it also in GCP Project Auth 2.0. Why in both?
          echo "GOOGLE_REDIRECT_URIS=https://gg6svciji8.execute-api.us-east-1.amazonaws.com/play1/api/v1/googleAccount/getCode" >> $GITHUB_ENV
          echo "GOOGLE_EMAIL_ADDRESS=<EMAIL>"  >> $GITHUB_ENV
          echo "GOOGLE_USER_EXTERNAL_USERNAME=<EMAIL>"  >> $GITHUB_ENV

          echo "GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth" >> $GITHUB_ENV
          # TODO Currently we have both contact-google... repo variable and hardcoded here, which one shall we use?
          echo "GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token" >> $GITHUB_ENV
          echo "GOOGLE_CONTACT_SEQ_START=0" >> $GITHUB_ENV
          echo "GOOGLE_CONTACT_SEQ_END=5" >> $GITHUB_ENV
        else
          echo "Not a Google Authentication repo"
        fi

        IS_NEED_AWS_ACCESS=false

        # Must be before AWS Access
        # TODO Can we avoid providing AWS_DEFAULT_STORAGE_BUCKET_NAME to google-sheets-local and delete the bellow link? \
        if ${{ inputs.repo-name == 'storage-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'internet-domain-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package'}} \
          || ${{ inputs.repo-name == 'profile-reddit-restapi-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-user-external-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-sheets-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-contact-local-python-package'}}; then
          echo "Setup AWS Storage Environment Variables"
          echo "AWS_DEFAULT_STORAGE_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          IS_NEED_AWS_ACCESS=true
        else
          echo "Not AWS Storage repo"
        fi

        if [[ -z "${IS_NEED_AWS_ACCESS}" ]]; then
          echo "IS_NEED_AWS_ACCESS is undefined"
        #else
          #echo IS_NEED_AWS_ACCESS="${IS_NEED_AWS_ACCESS}"
        fi

        # Must be after AWS Storage
        echo is_need_aws_access=$IS_NEED_AWS_ACCESS
        if [[ "$IS_NEED_AWS_ACCESS"=='true' ]]; then
          echo "Setup AWS Access Environment Variables"
          #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
          echo "AWS_ACCESS_KEY_ID=${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not AWS Access repo"
        fi

        # TODO opensearch-local-python-package
        if ${{ inputs.repo-name == 'opensearch-local-python-package' ||
          inputs.repo-name == 'event-main-local-restapi-python-package-serverless-com' ||
          inputs.repo-name == 'criteria-main-local-python-package' }}; then
          echo "Setup OPENSEARCH Environment Variables"
          echo "OPENSEARCH_HOST=${{ vars[format('OPENSEARCH_HOST_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "OPENSEARCH_INITIAL_ADMIN_PASSWORD=${{ secrets[format('OPENSEARCH_INITIAL_ADMIN_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not OPENSEARCH repo"
        fi

    - name: sql2code step (this step should run after "Set environment variables" step and before the "tests" step)
      if: ${{ inputs.sql2code-command-line }}
      #if: false
      run: |
        #echo "--- root ls -lag"
        #ls -lag

        #cd sql2code # ${{ inputs.repo-directory }}

        #pwd
        #echo "--- ls after cd to sql2code"
        #ls -lag

        # --------------------------------------------------------------------
        #echo "sql2code has dev branch - and can't be used until we merge or fix .gitmodule"
        #echo "--- ls -lag sql2code"
        #ls -lag sql2code 2>/dev/null
        
        #echo "--- sql2code/sql2code/sqltocode_local_python"
        #ls -lag ${{ inputs.repo-directory }}/sql2code/sqltocode_local_python 2>/dev/null

        #echo "--- sql2code/sql2code/sqltocode_local_python/sql_to_code"
        #ls -lag ${{ inputs.repo-directory }}/sql2code/sqltocode_local_python/sql_to_code 2>/dev/null

        #echo "--- sql2code/sql2code/sqltocode_local_python/sql_to_code/src"
        #ls -lag sql2code/sqltocode_local_python/sql_to_code/src 2>/dev/null

        #echo "--- ls -lag sql2code/database_mysql_local"
        #ls -lag database_mysql_local

        # --------------------------------------------------------------------
        #echo "--- ls -lag database-mysql-local-python-package"
        #ls -lag database-mysql-local-python-package 2>/dev/null

        # Submodule
        #echo "--- ls -lag sql2code"
        #ls -lag sql2code 2>/dev/null

        #echo "--- ls -lag sql2code/sqltocode_local_python"
        #ls -lag sql2code/sqltocode_local_python 2>/dev/null

        #echo "--- ls -lag sql2code/sqltocode_local_python/sql_to_code_local"
        #ls -lag sql2code/sqltocode_local_python/sql_to_code_local 2>/dev/null

        #echo "--- ls -lag sql2code/sqltocode_local_python/sql_to_code_local/src"
        #ls -lag sql2code/sqltocode_local_python/sql_to_code_local/src 2>/dev/null


        # TODO All those are mandatory? Can we avoid doing it twice?
        echo --- pip install build
        pip install build
        # echo "--- 1a ls -lag"
        # ls -lag
        # echo "--- 1b ls -lag sql2code-local-python-package"
        # ls -lag sql2code-local-python-package
        # echo "--- cd sql2code-local-python-package (so we can run the build)"
        cd sql2code-local-python-package
        echo --- python -m build 
        python -m build
        
        echo --- python -m pip install --upgrade pip
        # echo "ls -lag setup.py pyproject.toml"
        # ls -lag setup.py pyproject.toml
        python -m pip install --upgrade pip

        echo --- pip install -r requirements.txt
        pip install -r requirements.txt

        #echo "--- Which version do we use?"
        #pip show database-mysql-local
        # Not working
        #pip show sql-to-code-local

        #echo --- ls -lag sql2code/sqltocode_local_python/sql_to_code_local/src
        #ls -lag sql2code/sqltocode_local_python/sql_to_code_local/src

        # echo "--- pwd1 xxxx"
        # pwd

        # echo "ls -lag"
        # ls -lag

        # echo "cd ~"
        # cd ~

        # echo "ls -lag"
        # ls -lag

        # echo "cd work"
        # cd work

        # echo "ls -lag (inside work)"
        # ls -lag

        
        # TODO This is not working when we run the Sql2Code play1 GHA
        # cd ~/work/${{ inputs.repo-directory }}
        # Trying to make Sql2Code play1 GHA to work
        cd ~/work/sql2code-local-python-package

        # echo "--- A pwd2"
        # pwd
        # ls -lag
        # echo "--- B cd ${{ inputs.repo-directory }}"
        cd ${{ inputs.repo-directory }}
        # pwd
        # ls -lag

        # echo "--- C cd ${{ inputs.repo-directory }}"
        cd ${{ inputs.repo-directory }}
        
        echo "--- 888 pwd"
        pwd
        ls -lag
        
        echo "--- Running sql2code command line (part of the test before publishing)"
        # TODO replace directory names with variables
        python ./sql2code_local/src/sqltocode.py ${{ inputs.sql2code-command-line }}
        #python ~/${{ inputs.repo-name }}/sql2code/sqltocode_local_python/sql_to_code_local/src/sqltocode.py ${{ inputs.sql2code-command-line }}


        # TODO Not working
        #cat table_definition.py

        # We don't know in the Reusable Workflow which files are created/updated by Sql2Code
        #ls -lag

        #git status
        #git log
        
        #ls -lag
        # --force or -f
        #echo "--- git add --all --force"
        # Adds two files, instead of one file
        #git add --all --force
        echo --- git add ${{ inputs.package-directory }}/src/\*
        git add ${{ inputs.package-directory }}/src/\*
        git status
        git log

        # To resolve "Author identity unknown" in "git commit"
        git config --global user.email "<EMAIL>"
        git config --global user.name "Tal Rosenberger"

        echo "--- git commit -m"
        git commit -m "Updates done by Sql2Code using GHA" || echo "No changes to commit"
        git status
        git log

        echo "--- git push is not using due to Write Access error"
        # TODO We got the error "remote: Write access to repository not granted."
        #git push
        #git status
        #git log
      env:
        # TODO: Please comment and delete all environment variables not being used.

        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        # TODO Values from https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        #LOGGER_MINIMUM_SEVERITY: Warning
        #LOGGER_MINIMUM_SEVERITY: 1
        LOGGER_MINIMUM_SEVERITY: 501
        # TODO How do we avoid this variable if it is not mentioned?
        LOGGER_IS_WRITE_TO_SQL: ${{ inputs.logger-is-write-to-sql }}
        DEBUG: 0

        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # TODO Is it mandatory? - We comment it if we include it in the "Set environment variables" step
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        # Sql2Code might need permissions that are not the microservice production runtime permissions
        #RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
        #RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}

        # TODO Is it mandatory
        #REDIS_PORT: ${{ vars[format('REDIS_PORT_{0}', inputs.environment-name)] }}


    # TODO: As we didn't manage to use run_python_tests.yml, please sync this step with run_python_tests.yml
    # TODO Can we replace this with run_python_tests.yml? - We didn't manage to call run_python_tests.yml from a step.
    # TODO: Please write Unit-Test in /<project-name>/tests directory
    # TODO: Please create <project-name>-graphql/restapi-python-package in a separate repo to test the published package
    - name: Test methods/functions on GitHub runner using pytest (Tests of the published package should be done by the <project-name>-graphql/restapi-serverless-com E2E Tests)
      run: |
        cd ./${{ inputs.repo-directory }}
        pip install pytest-cov
        # Error @pytest.fixture def mocker_get_variable_value_by_variable_id(mocker): E fixture 'mocker' not found
        # pytest-mock needed for variable-local-python-package
        date
        pip install pytest-mock
        date
        # TODO: Fix this line (i.e. --cov=...) so it will work with your modules
        #pytest
        #python -m pytest
        #pytest # Instead of python -m pytest
        #python -m unittest discover -s tests -p 'test_*.py'
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app tests/ | tee pytest-coverage.txt
        #(pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=$package_name $package_name/tests/ | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0)
        # This is from database-mysql-local-python-package, creating coverage report, not generating pytest-coverage.txt and pytest.xml
        # Akiva Skolnik:
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        #pytest -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        # Gil Azani:
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        # pytest Combination
        # Also in https://github.com/circles-zone/github-workflows/edit/main/.github/workflows/python_serverless_com.yml
        set -o pipefail
        if ${{ inputs.is_run_selenium == true }}; then
          echo "Selenium On"
          #wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
          #sudo dpkg -i google-chrome-stable_current_amd64.deb
          #sudo apt --fix-broken install
        else
          echo Selenium Off
        fi
        pip show pytest
        date
        # -x flag to pytest to make it stop after the first error
        pytest -x -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        date
      env:
        # TODO: Please comment and delete all environment variables not being used.

        # inputs.environment_name we don't need to add it to the testing env:
        # inputs.environment-name is the standard, we need to add it to the testing env:
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}

        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        # TODO Values from https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        LOGGER_MINIMUM_SEVERITY: ${{ inputs.logger-minimum-severity }}
        LOGGER_IS_WRITE_TO_SQL: ${{ inputs.logger-is-write-to-sql }}

        #PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        #PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # We need it also in tests of remote
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        ##RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        ##RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
        # Sanity Tests might need permissions that are not the microservice production runtime permissions
        #RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}  

        #REGION: ${{ vars.AWS_DEFAULT_REGION }}
        #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
        #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        
        #BUCKET_NAME: ${{ vars[format('AWS_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}
        #AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}

        # import "from circles_local_aws_s3_storage_python.CirclesStorage import circles_storage".
        # TODO I think we should change BUCKET_NAME to AWS_DEFAULT_STORAGE_BUCKET_NAME
        #BUCKET_NAME: 'storage.us-east-1.play1.circ.zone'
        # TODO I think we should change REGION to AWS_DEFAULT_REGION
        #REGION: 'us-east-1'

        # https://github.com/circles-zone/google-contact-local-python-package
        #GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
        #GOOGLE_PROJECT_ID: ${{ secrets.GOOGLE_PROJECT_ID }}
        #GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
        #TODO Add GOOGLE_ prefix
        #GOOGLE_PORT_FOR_AUTHENTICATION: ${{ vars.PORT_FOR_AUTHENTICATION }}
        #TODO Shall we have an Environment Variable for this?
        #GOOGLE_REDIRECT_URIS: http://localhost
        #GOOGLE_AUTH_URI: http://localhost
        #GOOGLE_TOKEN_URI: http://localhost
        #GOOGLE_CONTACT_SEQ_START: 0
        #GOOGLE_CONTACT_SEQ_END: 5

        # Used by: message-local-python
        #Old: DEFAULT_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_DEFAULT_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}
        #New: MESSAGE_TEST_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}

        # Used by: messages-local-python
        #TEST_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_DEFAULT_FROM_PROFILE_ID_{0}', inputs.environment-name)] }}
        # Used by: email-message-aws-ses-local-python-package
        #FROM_EMAIL: ${{ vars[format('MESSAGE_FROM_EMAIL_{0}', inputs.environment-name)] }}
        #AWS_SES_DEFAULT_CONFIGURATION_SET: ${{ vars[format('MESSAGE_AWS_SES_DEFAULT_CONFIGURATION_SET_{0}', inputs.environment-name)] }}
        #TEST_DESTINATION_EMAIL: ${{ vars[format('MESSAGE_TEST_TO_EMAIL_{0}', inputs.environment-name)] }}
        #REALLY_SEND_EMAIL: ${{ vars[format('MESSAGE_REALLY_SEND_EMAIL_{0}', inputs.environment-name)] }}
        #TEST_DESTINATION_PHONE_NUMBER: ${{ vars[format('MESSAGE_TEST_TO_PHONE_NUMBER_{0}', inputs.environment-name)] }}

        #OPENCAGE_KEY: ${{ secrets.OPENCAGE_KEY }}
        

    # TODO Maybe causing error "reusable workflows should be referenced at the top-level `jobs.*.uses' key, not within steps"
    #- name: NEW Test methods/functions on GitHub runner using pytest (Tests of the published package should be done by the <project-name>-graphql/restapi-serverless-com E2E Tests)
      #uses: circles-zone/github-workflows/.github/workflows/run_python_tests.yml@main
      #with:
        ##repo-name: message-local-python-package
        #repo-directory: message_local_python_package

    - name: Remove Github Actions IP from a security group
      #if: always()
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 revoke-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # Since RDS/MySQL in Master/Management AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}

    # Errors: Create commit comment
    # Error: HttpError: Resource not accessible by integration
    # Error: Resource not accessible by integration
    # Solution: permissions: contents: write

    - name: ls
      #if: always()
      if: false
      run: |
        echo --- root
        ls -lag
        echo --- repo directory ./${{ inputs.repo-directory }}
        ls -lag ./${{ inputs.repo-directory }}
        echo --- package directory ./${{ inputs.repo-directory }}/${{ inputs.package_name }}
        ls -lag ./${{ inputs.repo-directory }}/$package_name

    # TODO: Please create pytest-coverage.txt and pytest.xml before this step
    - name: Pytest coverage comment
      if: ${{ false }}
      #if: github.ref == 'refs/heads/master'
      id: coverageComment
      #uses: MishaKav/pytest-coverage-comment@main # https://github.com/MishaKav/pytest-coverage-comment
      # Due to error message in https://github.com/circles-zone/user-main-local-python-package/ "are not allowed to be used in circles-zone/user-main-local-python-package. Actions in this workflow must be: within a repository owned by circles-zone, created by GitHub, or verified in the GitHub Marketplace."
      uses: circles-zone/pytest-coverage-comment@main # https://github.com/MishaKav/pytest-coverage-comment
      with:
        pytest-coverage-path: ./${{ inputs.repo-directory }}/pytest-coverage.txt
        pytest-xml-coverage-path: ./coverage.xml
        junitxml-path: ./${{ inputs.repo-directory }}/pytest.xml
        github-token: ${{ secrets.GITHUB_TOKEN }}

    - name: Check the output coverage
      if: ${{ false }}
      run: |
        echo "Coverage Percentage - ${{ steps.coverageComment.outputs.coverage }}"
        echo "Coverage Color - ${{ steps.coverageComment.outputs.color }}"
        #echo "Coverage Html - ${{ steps.coverageComment.outputs.coverageHtml }}"
        #echo "Summary Report - ${{ steps.coverageComment.outputs.summaryReport }}"
        #echo "Coverage Warnings - ${{ steps.coverageComment.outputs.warnings }}"
        #echo "Coverage Errors - ${{ steps.coverageComment.outputs.errors }}"
        #echo "Coverage Failures - ${{ steps.coverageComment.outputs.failures }}"
        #echo "Coverage Skipped - ${{ steps.coverageComment.outputs.skipped }}"
        #echo "Coverage Tests - ${{ steps.coverageComment.outputs.tests }}"
        #echo "Coverage Time - ${{ steps.coverageComment.outputs.time }}"
        #echo "Not Success Test Info - ${{ steps.coverageComment.outputs.notSuccessTestInfo }}"

    # I didn't manage to use it in database-mysql-local-python
    # dephell deps convert --from pyproject.toml --from-format poetry --to setup.py --to-format setuppy

    # Worked for me in person-local dvlp1 (without reusable workflow)
    # Not working for me in GitHub python_package.yml reusable workflow, I tried to add "user: __token__". We should try it.
    - name: Publish the Package to TestPyPI
      if: inputs.is-publish-to-test-pypi == true
      uses: pypa/gh-action-pypi-publish@release/v1 # v1.8.11 # master is not updated https://github.com/pypa/gh-action-pypi-publish
      with:
        user: __token__
        packages-dir: ${{ inputs.repo-directory }}/dist
        repository-url: https://test.pypi.org/legacy/
        verbose: true  # For debugging

    - name: Publish the package to non-TestPyPI
      #if: ${{ inputs.is-publish-to-non-test-pypi == true
      if: ${{ inputs.is-publish-to-non-test-pypi == true && contains(github.event.head_commit.message, '[pub]') }}
      #uses: pypa/gh-action-pypi-publish@v1.8.11 # master is not updated https://github.com/pypa/gh-action-pypi-publish
      uses: pypa/gh-action-pypi-publish@v1.12.4 # master is not updated https://github.com/pypa/gh-action-pypi-publish
      with:
        user: __token__
        password: ${{ secrets.PYPI_API_TOKEN }}
        packages-dir: ${{ inputs.repo-directory }}/dist 
        #packages-dir: ${{ inputs.repo-directory }}/${{ inputs.package_directory_name }}/dist
        #packages-dir: ./dist 
        verbose: true  # For debugging


    - name: pycallgraph
      #if: always()
      if: false
      run: |
        cd ./${{ inputs.repo-directory }}
        # pycallgraph depricated so I changed it to pycallgraph2
        pip install pycallgraph2
        # TODO Where can I find the mypythonscript.py
        #pycallgraph graphviz -- ./mypythonscript.py

# TODO Run a few Python and TypeScript packages that use this package to make sure we didn't break anything
