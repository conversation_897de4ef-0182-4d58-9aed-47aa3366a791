# Was used by: run_python_package.yml
# Become exactly like run_pytohn_package.yml so we don't need/use this file !!!!!!!!!!!!!
# TODO We didn't manage to use it by: publish_python_package.yml

name: Run Python Tests Workflow

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: true
        type: string
      branch-name:
        required: false
        type: string
        default: dev
      repo-directory:
        required: true
        type: string
      google-user:
        required: false
        type: string
        # Can'<NAME_EMAIL> as we don't want the login to run
        #default: '<EMAIL>'
        default: ''
      logger-minimum-severity:
        required: false
        type: string
        default: Warning
      is-run-local:
        required: false
        type: string
        default: false
      is-run-remote:
        required: false
        type: string
        default: false
      # We should try to run create-user, smartlink with is-run-anonymous true
      is-run-anonymous:
        required: false
        type: string
        default: false
jobs:
  run_python_tests:
    name: Run Python Tests Job (without Build) ${{ inputs.repo-name }} used both by Publish and Run - Still not working
    if: ${{contains(github.event.head_commit.message, '[tst]') || contains(github.event.head_commit.message, '[pub]') }}
    runs-on: ubuntu-latest
    steps:
    #TODO Can we avoid checkout?
    - name: Checkout code
      uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
        # Repository name with owner. For example, actions/checkout
        # Default: ${{ github.repository }}
        repository: circles-zone/${{ inputs.repo-name }}
        # TODO Maybe we need to add `secrets: inherit` in the calling Workflow
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
        #ref: "${{ github.head_ref || github.ref }}"
        ref: ${{ inputs.branch-name }}

    # To resolve "ModuleNotFoundError: No module named"
    - name: Install dependencies
      run: |
        cd ./${{ inputs.repo-directory }}
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    # TODO How can we share this code between run_python, publish_python, run_typescript and deploy_typescript?
    - name: Set environment variables
      run: |
        #echo "SLACK_USERNAME=Github Actions" >> $GITHUB_ENV
        #echo "SLACK_ICON_EMOJI=:ocotcat:" >> $GITHUB_ENV
        #echo "SLACK_TITLE=This is a GitHub Actions build!" >> $GITHUB_ENV
        #echo "SLACK_WEBHOOK=${{ secrets.SLACK_WEBHOOK }}" >> $GITHUB_ENV
        #echo "SLACK_CHANNEL=#channel" >> $GITHUB_ENV
  
        #if [[ ${{ needs.build.result }} == success ]]; then
        if [[ ${{ !inputs.is-run-anonymous  }} ]]; then
          echo "PRODUCT_USER_IDENTIFIER=${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "PRODUCT_PASSWORD=${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        #else
        fi

        if [[ ${{ inputs.is-run-local }} ]]; then
          echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
          echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        #else # inputs.is-run-remote
        fi

        # TODO I'm not sure what is REDDIT_USERNAME - I'm guessing
        if [[ ${{ inputs.repo-name == 'profile-reddit-restapi-imp-local-python-package' }} ]]; then
          echo "Setup REDDIT Environment Variables"
          echo "REDDIT_USERNAME=${{ vars.REDDIT_CLIENT_ID }}" >> $GITHUB_ENV
        else
          echo "Not REDDIT repo"
        fi

        if ${{ inputs.repo-name == 'google-contact-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-account-local-python-package' }}; then
          echo "Setup Google Authentication Environment Variables2"
          echo "GOOGLE_CLIENT_ID=${{ vars[format('GOOGLE_CLIENT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_CLIENT_SECRET=${{ secrets[format('GOOGLE_CLIENT_SECRET_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # TODO Do we need this
          echo "GOOGLE_PROJECT_ID=${{ vars[format('GOOGLE_PROJECT_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "GOOGLE_PORT_FOR_AUTHENTICATION=${{ vars.GOOGLE_PORT_FOR_AUTHENTICATION }}" >> $GITHUB_ENV
          #echo "GOOGLE_REDIRECT_URIS=http://localhost:54219/" >> $GITHUB_ENV
          # TODO Will it work in GHA? No, Interactive Google Authentication do not work in GHA. Do we need it here? If so, can we make it GHA Variable and not hard coded? We have it also in GCP Project Auth 2.0. Why in both?
          echo "GOOGLE_REDIRECT_URIS=https://gg6svciji8.execute-api.us-east-1.amazonaws.com/play1/api/v1/googleAccount/getCode" >> $GITHUB_ENV
    
          echo "GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth" >> $GITHUB_ENV
          # TODO Currently we have both contact-google... repo variable and hardcoded here, which one shall we use?
          echo "GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token" >> $GITHUB_ENV

        else
          echo "Not a Google Authentication repo"
        fi

        if ${{ inputs.repo-name == 'google-contact-local-python-package' }}; then
          echo "Setup Google Contact (People API) Environment Variables"
          echo "GOOGLE_CONTACT_SEQ_START=0" >> $GITHUB_ENV
          echo "GOOGLE_CONTACT_SEQ_END=5" >> $GITHUB_ENV
          echo "GOOGLE_EMAIL_ADDRESS=<EMAIL>" >> $GITHUB_ENV
          echo "GOOGLE_USER_EXTERNAL_USERNAME=<EMAIL>" >> $GITHUB_ENV
        else
          echo "Not a Google Contact (People API) repo"
        fi

        if ${{ inputs.repo-name == 'location-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'real-estate-realtor-com-selenium-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-location-local-python-package' }} \
          || ${{ inputs.repo-name == 'google-contact-local-python-package'}} ; then
          echo "Setup Opencage Environment Variables"
          echo "OPENCAGE_KEY=${{ secrets.OPENCAGE_KEY }}" >> $GITHUB_ENV
        else
          echo "Not Opencage repo"
        fi


        IS_NEED_AWS_ACCESS=false

        # Must be before AWS Access
        if ${{ inputs.repo-name == 'storage-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-main-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-zoominfo-graphql-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'internet-domain-local-python-package' }} \
          || ${{ inputs.repo-name == 'profile-facebook-selenium-scraper-imp-local-python-package' }} \
          || ${{ inputs.repo-name == 'contact-person-profile-csv-imp-local-python-package'}} \
          || ${{ inputs.repo-name == 'google-contact-local-python-package'}}; then
          echo "Setup AWS Storage Environment Variables"
          echo "AWS_DEFAULT_STORAGE_BUCKET_NAME=${{ vars[format('AWS_DEFAULT_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          IS_NEED_AWS_ACCESS=true
        else
          echo "Not AWS Storage repo"
        fi

        if [[ -z "${IS_NEED_AWS_ACCESS}" ]]; then
          echo "IS_NEED_AWS_ACCESS is undefined"
        #else
          #echo IS_NEED_AWS_ACCESS="${IS_NEED_AWS_ACCESS}"
        fi

        # Must be after AWS Storage
        echo is_need_aws_access=$IS_NEED_AWS_ACCESS
        if [[ "$IS_NEED_AWS_ACCESS"=='true' ]]; then
          echo "Setup AWS Access Environment Variables"
          #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
          echo "AWS_ACCESS_KEY_ID=${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not AWS Access repo"
        fi


    # https://www.daimto.com/google-authentication-with-curl/
    - name: Google Login Using Refresh Token (Still not working) - Please ignore if it is Red. Do we need it?
      #if: ${{ inputs.google-user=='<EMAIL>' }} 
      if: false
      run: |
        # TODO Use ${{ inputs.google-user }}
        ApplicationClientId=${{ vars.GOOGLE_CLIENT_ID }}
        echo ApplicationClientId=$ApplicationClientId
        ApplicationClientSecret=${{ secrets.GOOGLE_CLIENT_SECRET }}
        echo ApplicationClientSecret=$ApplicationClientSecret
        curl \
        –request POST \
        –data ‘client_id=$ApplicationClientId&client_secret==$ApplicationClientSecret&refresh_token=[Refresh token granted by second step]&grant_type=refresh_token’ \
        https://accounts.google.com/o/oauth2/token

    # I added this code both in google-contact-local-python-package GHA YML and run_python_tests.yml
    # https://stackoverflow.com/questions/8927936/export-gmail-contacts-via-unattended-script
    - name: Google Login (Still not working) - Please ignore if it is Red
      if: ${{ inputs.google-user=='<EMAIL>' }} 
      run: |
        # TODO Use ${{ inputs.google-user }}
        Email=<EMAIL>
        Passwd=Yalp1!Circ
        echo 1
        echo $Email
        echo 2
        aaa=${Email}
        echo $aaa
        Auth=$(curl --silent https://www.google.com/accounts/ClientLogin --data-urlencode Email=${Email} --data-urlencode Passwd=${Passwd} -d accountType=GOOGLE -d source=Google cURL-Example -d service=cp | grep Auth | cut -d= -f2)
        echo 3 $Auth
        echo $Auth
        echo 4
        echo ${dir}
        echo 5
        echo ${tmpfile}
        echo 6
        #curl -o ${dir}/${tmpfile} --silent --header "Authorization: GoogleLogin auth=$Auth" "https://www.google.com/m8/feeds/contacts/$Email/full?v=3.0&q=$2&alt=$Format"
        curl --silent --header "Authorization: GoogleLogin auth=$Auth" "https://www.google.com/m8/feeds/contacts/$Email/full?v=3.0&q=$2&alt=$Format"

        
      # TODO: Please write Unit-Test in /<project-name>/tests directory
      # TODO: Please create <project-name>-graphql/restapi-python-package in a separate repo to test the published package
    - name: Test methods/functions on GitHub runner using pytest (Tests of the published package should be done by the <project-name>-graphql/restapi-serverless-com E2E Tests)
      run: |
        cd ./${{ inputs.repo-directory }}
        pip install pytest-cov
        # Error @pytest.fixture def mocker_get_variable_value_by_variable_id(mocker): E fixture 'mocker' not found
        # pytest-mock needed for variable-local-python-package
        pip install pytest-mock
        # TODO: Fix this line (i.e. --cov=...) so it will work with your modules
        #pytest
        #python -m pytest
        #pytest # Instead of python -m pytest
        #python -m unittest discover -s tests -p 'test_*.py'
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app tests/ | tee pytest-coverage.txt
        #(pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=$package_name $package_name/tests/ | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0)
        # This is from database-mysql-local-python-package, creating coverage report, not generating pytest-coverage.txt and pytest.xml
        # Akiva Skolnik:
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        #pytest -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        # Gil Azani:
        #pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        # pytest Combination
        # Also in https://github.com/circles-zone/github-workflows/edit/main/.github/workflows/python_serverless_com.yml
        # pytest-mock needed for variable-local-python-package
        pip install pytest-mock
        set -o pipefail
        pytest -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
      env:
        # TODO: Please comment and delete all environment variables not being used.
  
        # inputs.environment_name we don't need to add it to the testing env:
        # inputs. is the standard, we need to add it to the testing env:
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}
  
        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        # TODO Values from https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        LOGGER_MINIMUM_SEVERITY: ${{ inputs.logger-minimum-severity }}
        LOGGER_IS_WRITE_TO_SQL: ${{ inputs.logger-is-write-to-sql }}
  
        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}
  
        # We need it also in tests of remote
        #RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
        #RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }} 
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        #RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}
  
        #REGION: ${{ vars.AWS_DEFAULT_REGION }}
        #AWS_DEFAULT_REGION: ${{vars.AWS_DEFAULT_REGION}}
        
        #AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}
        
        #BUCKET_NAME: ${{ vars[format('AWS_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}
        #AWS_DEFAULT_STORAGE_BUCKET_NAME: ${{ vars[format('AWS_STORAGE_BUCKET_NAME_{0}', inputs.environment-name)] }}
  
        # TODO Should be included only in circles-zone/profile-reddit-restapi-imp-local-python-package
        #REDDIT_CLIENT_ID: ${{ vars.REDDIT_CLIENT_ID }}
        #REDDIT_CLIENT_SECRET: ${{ secrets.REDDIT_CLIENT_SECRET }}
  
        # https://github.com/circles-zone/google-contact-local-python-package
        #GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
        #GOOGLE_PROJECT_ID: ${{ secrets.GOOGLE_PROJECT_ID }}
        #GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
        #PORT_FOR_AUTHENTICATION: ${{ vars.PORT_FOR_AUTHENTICATION }}
  
        # Used by: message-local-python
        #DEFAULT_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_DEFAULT_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}
        #TEST_SENDER_PROFILE_ID: ${{ vars[format('MESSAGE_TEST_SENDER_PROFILE_ID_{0}', inputs.environment-name)] }}
        # Used by: email-message-aws-ses-local-python-package
        #FROM_EMAIL: ${{ vars[format('MESSAGE_FROM_EMAIL_{0}', inputs.environment-name)] }}
        #AWS_SES_DEFAULT_CONFIGURATION_SET: ${{ vars[format('MESSAGE_AWS_SES_DEFAULT_CONFIGURATION_SET_{0}', inputs.environment-name)] }}
        #TEST_DESTINATION_EMAIL: ${{ vars[format('MESSAGE_TEST_DESTINATION_EMAIL_{0}', inputs.environment-name)] }}
        #REALLY_SEND_EMAIL: ${{ vars[format('MESSAGE_REALLY_SEND_EMAIL_{0}', inputs.environment-name)] }}
        #TEST_DESTINATION_PHONE_NUMBER: ${{ vars[format('MESSAGE_TEST_PHONE_NUMBER_{0}', inputs.environment-name)] }}
