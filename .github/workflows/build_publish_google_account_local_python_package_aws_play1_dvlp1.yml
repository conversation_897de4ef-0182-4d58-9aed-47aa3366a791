# Being used in https://github.com/circles-zone/google-account-local-python-package/edit/BU-2241--develop-google-account-local-python-package/.github/workflows/build_publish_contact_email_address_local_python_package.yml

# TODO Replace google-account-local-python-package with exactly the repo name
# TODO <repo-short-name> i.e. Contact Email Address Local Python -> ConEmaAddLocalP
# TODO <branch-name> i.e. BU-1234 xsakjdlsk fdsh fjds hfkjds hfkd fkd
# TODO <repo-directory>

# TODO <sanity-repo-name>
# TODO <sanity-repo-short-name>
# TODO <sanity-branch-name>
# TODO <sanity-branch-jira>
# TODO <sanity-package-directory>
# TODO <package-directory>

name: Build Publish google-account-local-python-package to PyPI.org play1 dvlp1
on:
  push:
    branches: [ "BU-*", dev ]
    # TODO We prefer to run the GHA only on Pull Requests and not on every Push, but we can't as we use the same YML file for all environments using "if: startsWith(github.ref, 'refs/heads/bu-')"
    #pull_request:
    #branches: [ dev ]
jobs:
  # play1 Build, Test & Publish
  publish-google-account-local-python-package-play1:
    name: GoogleAccountLocP()Play1 Build, Test & Publish
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunately, we can't use "on: pull_request:" - possible solutions 1. change the if 2. split GHA per environment
    if: startsWith(github.ref, 'refs/heads/bu-')
    #if: '! github.event.pull_request.draft'
    strategy:
      #fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # To allow the Reusable GitHub Action (i.e.  for publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To enable the Reusable GitHub Action (i.e.  for publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: google-account-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: google-account-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarily to true
      #logger-is-write-to-sql: false
      is-run-local: true
      #is-run-remote: true


  ## play1 Sanity Tests
  run-google-contact-local-python-package-play1-dev:
    name: GoogleContactLocP(dev)Play1 Run
    needs: publish-google-account-local-python-package-play1
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write  # For publish_python_package.yml@main
      pull-requests: write # For publish_python_package.yml@main
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      component-name: google-contact-local-python
      repo-name: google-contact-local-python-package
      repo-directory: google-contact-local-python-package
      logger-minimum-severity: 1
      google-user: <EMAIL>


  run-sanity-repo-name-play1-sanity-branch-jira-number:
    name: <sanity-repo-short-name>P(<sanity-branch-jira-number>)Play1 Run
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
    #needs: publish-google-account-local-python-package-play1
    strategy:
      fail-fast: false
      #matrix:
      #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: <sanity-repo-name>
      branch-name: <sanity-branch-name>
      repo-directory: <sanity-repo-directory>
      package-directory: <sanity-package-directory>
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      is-run-local: true
      #is-run-remote: true


  # dvlp1 Build, Test, and Publish
  # No need to publish in dvlp1, only to run
  # Is it enough to use run? No, if we want to publish the artifacts somewhere else
  publish-google-account-local-python-package-dvlp1-dev:
    name: GoogleAccountLocP(dev)Dvlp1 Build, Test & Publish
    if: github.ref == 'refs/heads/dev'
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # Required by publish_python_package.yml
      pull-requests: write # Required by publish_python_package.yml
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: google-account-local-python-package
      repo-directory: google-account-local-python-package
      is-run-local: true
      #is-run-remote: true


  run-sanity-repo-name-dvlp1-dev:
    name: <sanity-repo-short-name>P(dev)Dvlp1 Run
    #if: github.ref == 'refs/heads/dev'
    if: false
    #needs: publish-google-account-local-python-package-dvlp1-dev
    strategy:
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: <sanity-repo-name>
      branch-name: dev
      repo-directory: <sanity-repo-directory>
      is-run-local: true
      #is-run-remote: true
