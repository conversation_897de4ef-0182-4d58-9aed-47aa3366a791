# Was used by: run_python_package.yml
# Become exactly like run_pytohn_package.yml so we don't need/use this file !!!!!!!!!!!!!
# TODO We didn't manage to use it by: publish_python_package.yml

name: Run Python Tests Workflow

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: true
        type: string
      branch-name:
        required: false
        type: string
        default: dev
      repo-directory:
        required: true
        type: string
      google-user:
        required: false
        type: string
        # Can'<NAME_EMAIL> as we don't want the login to run
        #default: '<EMAIL>'
        default: ''
      logger-minimum-severity:
        required: false
        type: string
        default: Warning
      is-run-local:
        required: false
        type: string
        default: false
      is-run-remote:
        required: false
        type: string
        default: false
      # We should try to run create-user, smartlink with is-run-anonymous true
      is-run-anonymous:
        required: false
        type: string
        default: false
jobs:
  run_python_tests:
    name: Run Recent ${{ inputs.repo-name }} used both by Publish and Run - Still not working
    runs-on: ubuntu-latest
    steps:
    #TODO Can we avoid checkout?
    - name: Checkout code
      uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
        # Repository name with owner. For example, actions/checkout
        # Default: ${{ github.repository }}
        repository: circles-zone/${{ inputs.repo-name }}
        # TODO Maybe we need to add `secrets: inherit` in the calling Workflow
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
        #ref: "${{ github.head_ref || github.ref }}"
        ref: ${{ inputs.branch-name }}

  Execute-SQL:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment-name }}
    steps:
      - uses: actions/checkout@v3
      # This step is independent and can be executed in a separate job
      - name: Run the reports
        run: |
          # Option 1
          echo "cd ./${{ inputs.repo-directory}}/database/mysql/reports"
          cd ./${{inputs.repo-directory}}/database/mysql/reports
          echo pwd
          ls *.sql
          echo "Pipeline all *.sql to MySQL"
          cat *.sql | mysql -u ${{ vars.READ_ONLY_RDS_USERNAME_PLAY1 }} -p"${{ secrets.READ_ONLY_RDS_PASSWORD_PLAY1 }}" -h ${{ vars.RDS_HOSTNAME_PLAY1 }}


