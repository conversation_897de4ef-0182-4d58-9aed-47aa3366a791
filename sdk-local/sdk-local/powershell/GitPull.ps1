# Get and display the current script path and filename
Write-Host "Current script: $($MyInvocation.MyCommand.Path)"Bu-s

$scriptName = $MyInvocation.MyCommand.Name

if ( $Args.Count -eq 0 ) {
    Write-Host "No parameters, doing all default steps"
    # TODO Change all to true
    $isGitPull=$true
    $isGitPullOriginDev=$true
}
else {
    Write-Host "$scriptName : number of arguments are " $args.Count
    Write-Host "Arguments are "$args
    ForEach ($arg in $args){
        write-output $arg
        if ($arg -eq "All" -or $arg -eq "CurrentBranch" -or $arg -eq "gitpull") {
            $isGitPull=$true
        }
        if ($arg -eq "All" -or $arg -eq "OriginDev" -or $arg -eq "gitpullorigindev") {
            $isGitPullOriginDev=$true
        }
    }
}

# We need to do git pull origin dev before we try to change the directory Set-Location. After checking parameters
if ($isGitPull) {
    Write-Host "Pulling from dev"
    git pull
    $isSuccess = $LASTEXITCODE -eq 0
    if (-not $isSuccess) {
        Write-Host "❌ Git pull from dev failed. Exiting."
        exit 1
    }
    Write-Host "✅ Finish pulling from dev"
}
else {
    Write-Host "TODO You are not pulling from dev (this can cause problems i.e. not working on the latest version of the branch)"
}

if ($isGitPullOriginDev) {
    Write-Host "Pulling from origin dev"
    git pull origin dev
    $isSuccess = $LASTEXITCODE -eq 0
    if (-not $isSuccess) {
        Write-Host "❌ Git pull origin dev failed. Exiting."
        exit 1
    }
    Write-Host "✅ Finish pulling from origin dev"
}
else {
    Write-Host "TODO You are not pulling from dev (this can cause problems i.e. merge conflicts)"
}
