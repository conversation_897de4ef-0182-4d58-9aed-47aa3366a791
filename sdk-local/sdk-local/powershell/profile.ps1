# $Env:GIT_REPOS_HOME/sdk-local/powershell/profile.ps1

# ~/.config/powershell/Microsoft.PowerShell_profile.ps1 is calling this file

Write-Host "This is sdk-local/sdk-local/powershell/profile.ps1"

function Add-Path {
    param([string]$newPath)

    $currentPath = $Env:PATH
    # Write-Host "Current $Env:PATH is $currentPath"
    # Pause

    # Write-Host "Current path is $currentPath"
    if ($currentPath -notlike "*$newPath*") {
        Write-Host "Adding $newPath to PATH"
        $newPathValue = "${currentPath}:${newPath}"
        # Write-Host "The new path should be $newPathValue"
        # Do not work
        # [System.Environment]::SetEnvironmentVariable("Path", $newPathValue, [System.EnvironmentVariableTarget]::Machine)
        $Env:PATH = $newPathValue
    }
    else {
        # Write-Host "No need to add $newPath"
    }
    # Write-Host "Env:PATH=$Env:PATH"
    # Pause
}

# From pyenv init
# export PYENV_ROOT="$HOME/.pyenv"
# [ -d $PYENV_ROOT/bin ] && export PATH="$PYENV_ROOT/bin:$PATH"
# eval "$(pyenv init - pwsh)"
# Needed by make_py
$Env:PYENV_ROOT="$HOME/.pyenv"

Write-Host "profile.ps1: Env:GIT_REPOS_HOME=$Env:GIT_REPOS_HOME"

# TODO Added since we didn't manage to add the script directory to the path. We need to delete the bellow line
# $Env:GIT_REPOS_HOME = "/repos/circlez"
# Write-Host "GIT_REPOS_HOME is $Env:GIT_REPOS_HOME"

$PERSONAL_REPO_PATH="$Env:GIT_REPOS_HOME/$PERSONAL_REPO_NAME"

$Env:PATH += ";$PERSONAL_REPO_PATH/powershell"

# Temp - Reset the path without wrong values
# Add-Content $PROFILE -Value "`$Env:PATH = '/home/<USER>/.vscode/extensions/ms-python.python-2025.8.0-linux-x64/python_files/deactivate/bash:/home/<USER>/.pyenv/versions/3.13.3/bin:/opt/microsoft/powershell/7:/home/<USER>/.vscode/extensions/ms-python.python-2025.8.0-linux-x64/python_files/deactivate/bash:/home/<USER>/.pyenv/versions/3.13.3/bin:/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims:/home/<USER>/.pyenv/shims:/home/<USER>/.pyenv/bin:/path/to/apache-maven-3.9.9/bin:/home/<USER>/.nvm/versions/node/v22.16.0/bin:/home/<USER>/repos/circlez/.venv_circlez_3_13_3/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.lmstudio/bin:/home/<USER>/.lmstudio/bin'"
# Write-Host $Env:PATH
# Pause

# $Env:PATH="/home/<USER>/.vscode/extensions/ms-python.python-2025.8.0-linux-x64/python_files/deactivate/bash:/home/<USER>/.pyenv/versions/3.13.3/bin:/opt/microsoft/powershell/7:/home/<USER>/.vscode/extensions/ms-python.python-2025.8.0-linux-x64/python_files/deactivate/bash:/home/<USER>/.pyenv/versions/3.13.3/bin:/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims:/home/<USER>/.pyenv/shims:/home/<USER>/.pyenv/bin:/path/to/apache-maven-3.9.9/bin:/home/<USER>/.nvm/versions/node/v22.16.0/bin:/home/<USER>/repos/circlez/.venv_circlez_3_13_3/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.lmstudio/bin:/home/<USER>/.lmstudio/bin"
# Write-Host $Env:PATH
# Pause

# Write-Host "Script directory is $PSScriptRoot"

# Add-Content $PROFILE -Value "`$Env:PATH += ':$PSScriptRoot'"
# Write-Host $Env:PATH
# Pause

# For User-specific changes
$Folder = "$PERSONAL_REPO_PATH/powershell"
"Test to see if folder [$Folder]  exists"
if (Test-Path -Path $Folder) {
    Add-Path $Folder
} else {
    "TODO We recommand you to create directory or repo with your name i.e. ."
}

# TODO Create function that check that directory exists, if not exists clone a repo and execute command such as Add-Path
$Folder = "$Env:GIT_REPOS_HOME/python-sdk-remote-python-package/python-sdk-remote-python-package/powershell"
"Test to see if folder [$Folder]  exists"
if (Test-Path -Path $Folder) {
    Add-Path $Folder
} else {
    Write-Host "clone python-sdk-remote-python-package"
    git clone https://github.com/circles-zone/python-sdk-remote-python-package.git
    Add-Path $Folder
}

# Write-Host $Env:PATH
# $currentPath = [System.Environment]::GetEnvironmentVariable("Path", [System.EnvironmentVariableTarget]::User)
# Write-Host "Current path::User is $currentPath"
# $currentPath = [System.Environment]::GetEnvironmentVariable("Path", [System.EnvironmentVariableTarget]::Machine)
# Write-Host "Current path::Machine is $currentPath"
# $currentPath = $Env:PATH
# Write-Host "Current $Env:PATH is $currentPath"
# Pause

# Write-Host "Current path is $currentPath"
# if ($currentPath -notlike "*$newPath*") {
#     Write-Host "Adding $newPath to PATH"
#     $newPathValue = "${currentPath}:${newPath}"
#     Write-Host "The new path should be $newPathValue"
#     # Do not work
#     [System.Environment]::SetEnvironmentVariable("Path", $newPathValue, [System.EnvironmentVariableTarget]::Machine)
#     $Env:PATH = $newPathValue
#     Pause
# }
# else {
#     Write-Host "No need to add $newPath"
# }
# Write-Host "Env:PATH=$Env:PATH"
# Pause

# TODO Maybe it will work after reboot/restart
# Add-Content $PROFILE -Value "`$Env:PATH += ':$newPath'"

& $PERSONAL_REPO_PATH/powershell/set_git.ps1
& $PERSONAL_REPO_PATH/powershell/set_jira.ps1

#TODO Run set_python.ps1

# So make_py can execute Create-Draft-PR.ps1
Add-Path "$Env:GIT_REPOS_HOME/sdk-local/sdk-local/powershell"

# Add-Content $PROFILE -Value "`$Env:PATH += ':$newPath'"

# icacls.exe "$Env:GIT_REPOS_HOME/sdk-local/sdk-local/bin/makec.ps1" /grant "tal":(RX)

# Since the $Env:PATH didn't work
Set-Alias -Name makec -Value $Env:GIT_REPOS_HOME/sdk-local/sdk-local/powershell/makec.ps1 -Scope global
Set-Alias -Name find_duplicate_files -Value $Env:GIT_REPOS_HOME/sdk-local/sdk-local/powershell/find_duplicate_files.ps1 -Scope global

Set-Alias -Name make_ts -Value $Env:GIT_REPOS_HOME/typescript-sdk-remote-typescript-package/typescript-sdk-remote-typescript-package/powershell/make_ts.ps1 -Scope global
Set-Alias -Name make_py -Value $Env:GIT_REPOS_HOME/python-sdk-remote-python-package/python-sdk-remote-python-package/powershell/make_py.ps1 -Scope global
# alias | grep makec
# New-Alias makec $Env:GIT_REPOS_HOME/sdk-local/sdk-local/powershell/makec.ps1

# TODO If developer and PSScriptAnalyzer is not installed
Install-Module -Name PSScriptAnalyzer -Force

# If the user is not in one of the repos change directory to the repo root
$currentDir = Get-Location
$gitReposHome = $Env:GIT_REPOS_HOME
if ($currentDir.Path -notlike "$gitReposHome*") {
    Write-Host "Changing directory to $gitReposHome"
    Set-Location $gitReposHome
}