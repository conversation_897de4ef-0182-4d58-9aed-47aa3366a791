#!/usr/bin/env pwsh

# CreateDraftPr.ps1

# Interactive script to push changes and create GitHub draft PR

Write-Host "CreateDraftPr.ps1 (sdk-local)"

if ( $Args.Count -eq 0 ) {
    Write-Host "No parameters, doing all default steps"
    # TODO Change all to true
    $isPushSwitch=$true
    $isCreateDraftPrSwitch=$true
}
else {
    Write-Host "number of arguments are " $args.Count
    Write-Host "Arguments are "$args
    ForEach ($arg in $args){
        write-output $arg
        if ($arg -eq "All" -or $arg -eq "Push" -or $arg -eq "push") {
            $isPushSwitch=$true
            Write-Host "isPushSwitch=$isPushSwitch"
        }
        if ($arg -eq "All" -or $arg -eq "CreateDraftPr" -or $arg -eq "createDraftPr") {
            $isCreateDraftPrSwitch=$true
            Write-Host "isCreateDraftPrSwitch=$isCreateDraftPrSwitch"
        }
        if ($arg -eq "version") {
            if ($args[1]) {
                # TODO Works only if using "test filename"
                $packageVersion = $args[1]
                Write-Host "packageVersion=$packageVersion"
            }        
        }
    }
}
Pause

# Get version from setup.py (Python) or package.json (TypeScript)
if (Test-Path "setup.py") {
    # Python repository
    $version = (Select-String -Path "setup.py" -Pattern "version\s*=\s*['\`"]([^'\`"]+)['\`"]" | ForEach-Object { $_.Matches[0].Groups[1].Value })
} elseif (Test-Path "package.json") {
    # TypeScript repository
    $packageJson = Get-Content -Path "package.json" -Raw | ConvertFrom-Json
    $version = $packageJson.version
}
Write-Host "version=$version TODO shall we call it PACKAGE_VERSION?"

# TODO Shall we store the package version in a file?

$username = $env:USER

# Show git status before asking
git status
Write-Host "git status LASTEXITCODE=$LASTEXITCODE (Can we use it to check if there are any changes to push?)"
if ($isPushSwitch) {
    $isPush = Read-Host "Push changes to remote? (Y/n)"
    if ($isPush -match "^[Yy]" -or [string]::IsNullOrWhiteSpace($isPush)) {
        $currentDirectory = (Split-Path $PWD.Path -Leaf)
        $referenz = @('python', 'typescript', 'java')    
        $referenzRegex = [string]::Join('|', $referenz) # create the regex
        if ( $currentDirectory -match $referenzRegex ) {
            $runCI = Read-Host "Run CI (i.e. GHA) in the cloud? - Only if passed all tests locally (y/N)"
            if ($runCI -match "^[Yy]" -and -not [string]::IsNullOrWhiteSpace($isPush)) {
                Write-Host "Running GHA CI (this cost us money and it is fine if you are 100% sure you branch is ready for deployment production)"
                $isCI = $true
            } else {
                Write-Host "Not running GHA CI (this saves run money when you are not ready to create a PR)"
                $isCI = $false
            }
        } else {
            $isCI = $false
        }
        if ($isCI) {
            # TODO Add [pub] only if the git log -1 do not contain [pub]
            $defaultCommitMessage = "$(git log -1 --pretty=%B)"
            if ($defaultCommitMessage -notmatch "\[pub\]") {
                Write-Host "Adding [pub] $version to the commit message"
                $defaultCommitMessage = "[pub] $version $defaultCommitMessage"
            }
        } else {
            # $defaultCommitMessage = "$(git log -1 --pretty=%B)"
            # Remove [pub] from the commit message
            $defaultCommitMessage = "$(git log -1 --pretty=%B)" -replace '\[pub\]\s*|\[pub\]',''
        }

        Write-Host "defaultCommitMessage=$defaultCommitMessage"

        # Write-Host "git status"
        # git status
        $finalCommitMessage = Read-Host "Press enter to accept the default commit message [$($defaultCommitMessage)]"
        $finalCommitMessage = ($defaultCommitMessage,$finalCommitMessage)[[bool]$finalCommitMessage]

        if ($finalCommitMessage -notmatch "\[pub\]") {
            Write-Host "Adding [pub] to the commit message (as the user forgot to add it)"
            $finalCommitMessage = "[pub] $finalCommitMessage"
        }

        # TODO We should run this only if no file was changed
        # TODO Where do we setup GIT_REPOS_HOME?
        Write-Host "Touching $env:GIT_REPOS_HOME.gitignore in case no file was changed and we want to run the GHA"
        touch $env:GIT_REPOS_HOME.gitignore

        # git commit -a works better than `git ad`
        Write-Host "git commit -a -m '$finalCommitMessage'"
        # Git commit -a do not add .gitignore
        git add .gitignore
        git commit -a -m $finalCommitMessage

        Write-Host "git push"
        if ($username -eq "tal") {
            Write-Host "git push --force (To support pushing to dev. You might get warnings)"
            git push --force
            Write-Host "after git push --force"
        } else {
            git push
        }
        Write-Host "git status after push"
        git status
        if ($LASTEXITCODE -ne 0) { exit 1 }
    } # Push
} # isPush
else {
    Write-Host "Push was not requested"
}

# Both in feature branch and dev branch
if ($isCI) {
    Write-Host "Please check https://github.com/circles-zone/$currentDirectory/actions"
    Pause
}

$currentDirectory = (Split-Path $PWD.Path -Leaf)

$currentBranch = git branch --show-current
# Write-Host "currentBranch=$currentBranch"
$targetBranch = "dev"
if ($currentBranch -ne $targetBranch) {
    if ($isCreateDraftPrSwitch) {
        $createPR = Read-Host "If GHA is Full Green in https://github.com/circles-zone/$currentDirectory/actions, create GitHub draft pull request? (Y/n)"
        if ($createPR -match "^[Yy]" -or [string]::IsNullOrWhiteSpace($isPush)) {
            $defaultPushMessage = $currentBranch
            Write-Host "PR name should include the Jira Work Item (Jira Issue) number and full description of the task as written in the Jira task and you can add/update more things. It should include the full name of the developer"
            $finalPullRequestTitle = Read-Host "PR title [$($defaultPushMessage)]"
            Write-Host "After Read-Host finalPullRequestTitle=$finalPullRequestTitle"
            $finalPullRequestTitle = ($defaultPullRequestTitle,$finalPullRequestTitle)[[bool]$finalPullRequestTitle]
            Write-Host "After Assignment finalPullRequestTitle=$finalPullRequestTitle"
            # Get username from environment

            # Check if username exists in PR title
            if ($finalPullRequestTitle -notmatch $username) {
                $finalPullRequestTitle = "$finalPullRequestTitle ($username)"
            }
            else {
                Write-Host "found the username $username in the PR title $finalPullRequestTitle"
            }
            Write-Host "After making sure the username is in the PR title: finalPullRequestTitle=$finalPullRequestTitle"
            Pause

            # TODO This is not creating a draft PR yet
            Write-Host "gh pr create --draft --title '$finalPullRequestTitle'"
            gh pr create --draft --title "$finalPullRequestTitle"
            Write-Host "LASTEXITCODE=$LASTEXITCODE"
        } # createDraftPrUser
        else {
            Write-Host "User decided not to create draft PR"
        }
    } # isCreateDraftPrSwitch
    if ($runCI) {
        Write-Host "If GHA is Fully Green, change the Draft PR to a non-draft PR in https://github.com/$currentDirectory/pulls "
        Pause
        Write-Host "Check the files in the PR to make sure you are ok with all the changes included in the PR"
        Pause
        Write-Host "If you are ok with all the files included and the changes Slack the link to the PR (URL) to your Team Lead"
    }
}
