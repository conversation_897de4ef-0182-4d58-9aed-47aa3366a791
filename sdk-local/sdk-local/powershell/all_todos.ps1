
# Array of files to exclude from TODO search
$ExcludeFiles = @(
    "all_todos.ps1",
    "makec.ps1",
    "profile.ps1"
)

# Get the Git root directory if needed
$GIT_ROOT_HOME = git rev-parse --show-toplevel 2>$null

# Function to search for TODOs in a directory
function Find-TODOs {
    param (
        [string]$searchPath
    )

    # Search for TODOs in all files recursively, excluding specified files
    $todos = Get-ChildItem -Path $searchPath -Recurse -File |
        Where-Object {
            $fileName = Split-Path $_.Name -Leaf
            $fileName -notin $ExcludeFiles
        } |
        Select-String -Pattern "TODO" |
        ForEach-Object {
            [PSCustomObject]@{
                FileName = $_.Path
                LineNumber = $_.LineNumber
                TODO = $_.Line.Trim()
            }
        }

    return $todos
}

# First search current directory
$currentDirTodos = Find-TODOs -searchPath "."

# If no TODOs found and Git root exists, search Git root
if (-not $currentDirTodos -and $GIT_ROOT_HOME) {
    $todos = Find-TODOs -searchPath $GIT_ROOT_HOME
} else {
    $todos = $currentDirTodos
}

# Display results
if ($todos) {
    $todos | Format-Table -AutoSize -Wrap
} else {
    Write-Host "No TODOs found"
}