#!/bin/bash

# We should run this script with source, otherwise it will not change the $AWS_PROFILE

# bash script that set the environment based on a parameter while the default is play1

# Default environment is play1 if no parameter provided
ENV=${1:-play1}

# Set environment variable
# export ENVIRONMENT_NAME=$ENV
export AWS_PROFILE=$ENV

# echo "ENVIRONMENT_NAME set to: $ENVIRONMENT_NAME"
echo "AWS_PROFILE (Environment) set to: $AWS_PROFILE"

alias dvlp1="source set_aws_environment dvlp1"
alias play1="source set_aws_environment play1"
