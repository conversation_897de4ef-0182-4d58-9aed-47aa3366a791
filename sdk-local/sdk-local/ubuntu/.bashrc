# To run this file from bash I used `source ~/repos/circlez/sdk-local/sdk-local/ubuntu/bin/.bashrc`

echo circlez.ai circles-zone/sdk-local/sdk-local/ubuntu/.bashrc

# TODO Use the directory the user setup in her/his ~/.bashrc
if [ -f ~/repos/circlez/sdk-local/sdk-local/ubuntu/bin/.bash_aliases ]; then
    . ~/repos/circlez/sdk-local/sdk-local/ubuntu/bin/.bash_aliases
fi


# TODO Do we need this?
alias pyenvi="~/.pyenv/bin/pyenv"

echo "sdk-local/sdk-local/ubuntu/.bashrc Run pyenv from ~/.pyenv/bin/pyenv"

export PYENV_ROOT="$HOME/.pyenv"
echo "sdk-local/ubuntu/.bashrc PYENV_ROOT=$PYENV_ROOT"

# export PATH="$PYENV_ROOT/bin:$PATH"
export PATH="$HOME/.pyenv/bin:$PATH"

# If exists $GIT_REPOS_HOME/sdk-local then add $GIT_REPOS_HOME/sdk-local/sdk-local/ubuntu/bin to PATH
echo "sdk-local/ubuntu/.bashrc Checking if $GIT_REPOS_HOME/sdk-local/sdk-local/ubuntu/bin exists"
# ls -lag $GIT_REPOS_HOME/sdk-local/sdk-local/ubuntu/bin
if [ -d "$GIT_REPOS_HOME/sdk-local/sdk-local/ubuntu/bin" ]; then
    # Store the SDK local bin path in a variable
    SDK_LOCAL_BIN="$GIT_REPOS_HOME/sdk-local/sdk-local/ubuntu/bin"
    # Add the SDK local bin path to PATH only if it's not already in PATH
    if [[ ":$PATH:" != *":$SDK_LOCAL_BIN:"* ]]; then
        echo "sdk-local/ubuntu/.bashrc: Adding $SDK_LOCAL_BIN to PATH"
        export PATH="$SDK_LOCAL_BIN:$PATH"
    else
        echo "sdk-local/ubuntu/.bashrc: $SDK_LOCAL_BIN is already in PATH"
    fi
    # export PATH="$SDK_LOCAL_BIN:$PATH"
else
    echo "Please clone sdk-local repo to $GIT_REPOS_HOME"
    ls -lag $GIT_REPOS_HOME
    pushd $GIT_REPOS_HOME
    git clone https://github.com/circles-zone/sdk-local.git
    popd
fi

# To resolve the error: pyenv: shell integration not enabled. Run `pyenv init' for instructions.
echo "sdk-local/sdk-local/ubuntu/.bashrc penv init -"
eval "$(pyenv init -)"
eval "$(pyenv init --path)"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"

echo "sdk-local/ubuntu/.bashrc Changing directory"
echo "Checking if current directory is in GIT_REPOS_HOME=$GIT_REPOS_HOME"
# Write here script, If the user is not in subdirectory of GIT_REPOS_HOME environment variable then change the current directory to $GIT_REPOS_HOME
if [ -n "$GIT_REPOS_HOME" ]; then
    case "$PWD/" in
        "$GIT_REPOS_HOME"/*) ;;
        *) cd "$GIT_REPOS_HOME" ;;
    esac
fi

# TO Create play1 AWS profile (uses ~/.aws/credentials and ~/aws/config)
# aws configure --profile play1 --output json --region us-east-1
echo "Setup default AWS profile to play1"
# export AWS_PROFILE=play1
source set_aws_environment play1

